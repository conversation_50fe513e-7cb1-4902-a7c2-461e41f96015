<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总记录数</span>
            <i class="el-icon-document"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statistics.totalCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>收入记录</span>
            <i class="el-icon-plus" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #67C23A;">{{ statistics.incomeCount || 0 }}</div>
            <div class="card-sub">¥{{ statistics.totalIncomeAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>支出记录</span>
            <i class="el-icon-minus" style="color: #F56C6C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #F56C6C;">{{ statistics.expenseCount || 0 }}</div>
            <div class="card-sub">¥{{ statistics.totalExpenseAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>净变动</span>
            <i class="el-icon-money" style="color: #409EFF;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #409EFF;">¥{{ statistics.netAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="变动类型" prop="changeType">
        <el-select v-model="queryParams.changeType" placeholder="请选择变动类型" clearable>
          <el-option label="收入" value="1" />
          <el-option label="支出" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="收入类型" prop="incomeType">
        <el-select v-model="queryParams.incomeType" placeholder="请选择收入类型" clearable>
          <el-option label="任务佣金" value="1" />
          <el-option label="推荐奖励" value="2" />
          <el-option label="其他收入" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-input v-model="queryParams.businessType" placeholder="请输入业务类型" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['fuguang:balanceRecord:add']">新增</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:balanceRecord:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.userName }}</div>
          <div style="color: #909399; font-size: 12px;">ID: {{ scope.row.userId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="变动类型" align="center" prop="changeType" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.changeType === '1'" type="success">收入</el-tag>
          <el-tag v-else-if="scope.row.changeType === '2'" type="danger">支出</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="收入类型" align="center" prop="incomeType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.incomeType === '1'" type="primary" size="mini">任务佣金</el-tag>
          <el-tag v-else-if="scope.row.incomeType === '2'" type="success" size="mini">推荐奖励</el-tag>
          <el-tag v-else-if="scope.row.incomeType === '3'" type="warning" size="mini">其他收入</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="变动金额" align="center" prop="changeAmount">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.changeType === '1' ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">
            {{ scope.row.changeType === '1' ? '+' : '-' }}¥{{ scope.row.changeAmount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="变动前余额" align="center" prop="balanceBefore">
        <template slot-scope="scope">
          <span>¥{{ scope.row.balanceBefore }}</span>
        </template>
      </el-table-column>
      <el-table-column label="变动后余额" align="center" prop="balanceAfter">
        <template slot-scope="scope">
          <span>¥{{ scope.row.balanceAfter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessType" width="120">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ getBusinessTypeName(scope.row.businessType) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="业务单号" align="center" prop="businessNo" width="150" show-overflow-tooltip />
      <el-table-column label="变动说明" align="center" prop="description" width="200" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:balanceRecord:query']">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:balanceRecord:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:balanceRecord:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改余额变动记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="用户昵称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="变动类型" prop="changeType">
          <el-radio-group v-model="form.changeType">
            <el-radio label="1">收入</el-radio>
            <el-radio label="2">支出</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收入类型" prop="incomeType" v-if="form.changeType === '1'">
          <el-select v-model="form.incomeType" placeholder="请选择收入类型">
            <el-option label="任务佣金" value="1" />
            <el-option label="推荐奖励" value="2" />
            <el-option label="其他收入" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="变动金额" prop="changeAmount">
          <el-input v-model="form.changeAmount" placeholder="请输入变动金额" />
        </el-form-item>
        <el-form-item label="变动前余额" prop="balanceBefore">
          <el-input v-model="form.balanceBefore" placeholder="请输入变动前余额" />
        </el-form-item>
        <el-form-item label="变动后余额" prop="balanceAfter">
          <el-input v-model="form.balanceAfter" placeholder="请输入变动后余额" />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-input v-model="form.businessType" placeholder="请输入业务类型" />
        </el-form-item>
        <el-form-item label="业务ID" prop="businessId">
          <el-input v-model="form.businessId" placeholder="请输入业务ID" />
        </el-form-item>
        <el-form-item label="业务单号" prop="businessNo">
          <el-input v-model="form.businessNo" placeholder="请输入业务单号" />
        </el-form-item>
        <el-form-item label="变动说明" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入变动说明" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="记录详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ viewData.recordId }}</el-descriptions-item>
        <el-descriptions-item label="用户信息">{{ viewData.userName }} (ID: {{ viewData.userId }})</el-descriptions-item>
        <el-descriptions-item label="变动类型">
          <el-tag v-if="viewData.changeType === '1'" type="success">收入</el-tag>
          <el-tag v-else-if="viewData.changeType === '2'" type="danger">支出</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="收入类型" v-if="viewData.incomeType">
          <el-tag v-if="viewData.incomeType === '1'" type="primary">任务佣金</el-tag>
          <el-tag v-else-if="viewData.incomeType === '2'" type="success">推荐奖励</el-tag>
          <el-tag v-else-if="viewData.incomeType === '3'" type="warning">其他收入</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="变动金额">¥{{ viewData.changeAmount }}</el-descriptions-item>
        <el-descriptions-item label="变动前余额">¥{{ viewData.balanceBefore }}</el-descriptions-item>
        <el-descriptions-item label="变动后余额">¥{{ viewData.balanceAfter }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ getBusinessTypeName(viewData.businessType) }}</el-descriptions-item>
        <el-descriptions-item label="业务ID">{{ viewData.businessId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="业务单号">{{ viewData.businessNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="变动说明" span="2">{{ viewData.description }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
        }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBalanceRecord, getBalanceRecord, delBalanceRecord, addBalanceRecord, updateBalanceRecord, getBalanceRecordStatistics } from "@/api/fuguang/balanceRecord";

export default {
  name: "BalanceRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 余额变动记录表格数据
      recordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 查看数据
      viewData: {},
      // 统计数据
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        changeType: null,
        incomeType: null,
        businessType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
        changeType: [
          { required: true, message: "变动类型不能为空", trigger: "change" }
        ],
        changeAmount: [
          { required: true, message: "变动金额不能为空", trigger: "blur" },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
        ],
        balanceBefore: [
          { required: true, message: "变动前余额不能为空", trigger: "blur" }
        ],
        balanceAfter: [
          { required: true, message: "变动后余额不能为空", trigger: "blur" }
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "blur" }
        ],
        description: [
          { required: true, message: "变动说明不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询余额变动记录列表 */
    getList() {
      this.loading = true;
      listBalanceRecord(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getBalanceRecordStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 获取业务类型名称 */
    getBusinessTypeName(businessType) {
      const typeMap = {
        'task_commission': '任务佣金',
        'withdraw': '提现',
        'withdraw_reject': '提现拒绝',
        'admin_adjust': '管理员调整',
        'refund': '退款',
        'recommend_reward': '推荐奖励',
        'other_income': '其他收入'
      };
      return typeMap[businessType] || businessType;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        recordId: null,
        userId: null,
        userName: null,
        changeType: null,
        incomeType: null,
        changeAmount: null,
        balanceBefore: null,
        balanceAfter: null,
        businessType: null,
        businessId: null,
        businessNo: null,
        description: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加余额变动记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const recordId = row.recordId || this.ids
      getBalanceRecord(recordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改余额变动记录";
      });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.recordId != null) {
            updateBalanceRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          } else {
            addBalanceRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$modal.confirm('是否确认删除余额变动记录编号为"' + recordIds + '"的数据项？').then(function () {
        return delBalanceRecord(recordIds);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/balanceRecord/export', {
        ...this.queryParams
      }, `balanceRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.box-card {
  height: 100px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.card-header i {
  font-size: 20px;
}

.card-content {
  margin-top: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
