<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待支付订单</span>
          </div>
          <div class="text item">
            <i class="el-icon-time" style="color: #E6A23C;"></i>
            <span class="count">{{ statistics.pendingPayments || 0 }}</span>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付成功订单</span>
          </div>
          <div class="text item">
            <i class="el-icon-success" style="color: #67C23A;"></i>
            <span class="count">{{ statistics.successPayments || 0 }}</span>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>转账失败订单</span>
          </div>
          <div class="text item">
            <i class="el-icon-error" style="color: #F56C6C;"></i>
            <span class="count">{{ statistics.transferFailedPayments || 0 }}</span>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>活跃二维码</span>
          </div>
          <div class="text item">
            <i class="el-icon-qrcode" style="color: #409EFF;"></i>
            <span class="count">{{ qrcodeStatistics.activeQrcodes || 0 }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 最近支付订单 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近支付订单</span>
            <el-button style="float: right; padding: 3px 0" type="text"
              @click="$router.push('/offlinePayment/offlinePayment')">查看更多</el-button>
          </div>
          <el-table :data="recentPayments" border style="width: 100%" size="small">
            <el-table-column prop="orderNo" label="订单号" width="150" show-overflow-tooltip />
            <el-table-column prop="merchantName" label="商家" width="100" show-overflow-tooltip />
            <el-table-column prop="payAmount" label="金额" width="80">
              <template slot-scope="scope">
                <span style="color: #E6A23C;">￥{{ scope.row.payAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="payStatus" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.payStatus === '1' ? 'success' : 'warning'" size="mini">
                  {{ scope.row.payStatus === '1' ? '成功' : '待支付' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="时间">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 转账失败订单 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>转账失败订单</span>
            <el-button style="float: right; padding: 3px 0" type="text"
              @click="handleViewTransferFailed">处理失败</el-button>
          </div>
          <el-table :data="failedTransfers" border style="width: 100%" size="small">
            <el-table-column prop="orderNo" label="订单号" width="150" show-overflow-tooltip />
            <el-table-column prop="merchantName" label="商家" width="100" show-overflow-tooltip />
            <el-table-column prop="transferAmount" label="转账金额" width="80">
              <template slot-scope="scope">
                <span style="color: #F56C6C;">￥{{ scope.row.transferAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="transferTime" label="失败时间" width="120">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.transferTime, '{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="handleRetryTransfer(scope.row)">重试</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 商家二维码状态 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>商家二维码统计</span>
            <el-button style="float: right; padding: 3px 0" type="text"
              @click="$router.push('/offlinePayment/merchantQrcode')">管理二维码</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="statistic-item">
                <div class="statistic-title">总二维码数</div>
                <div class="statistic-content">{{ qrcodeStatistics.totalQrcodes || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="statistic-item">
                <div class="statistic-title">活跃二维码</div>
                <div class="statistic-content" style="color: #67C23A;">{{ qrcodeStatistics.activeQrcodes || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="statistic-item">
                <div class="statistic-title">停用二维码</div>
                <div class="statistic-content" style="color: #F56C6C;">{{ qrcodeStatistics.inactiveQrcodes || 0 }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getOfflinePaymentStatistics, listOfflinePayment, retryTransfer } from "@/api/fuguang/offlinePayment"
import { getMerchantQrcodeStatistics } from "@/api/fuguang/merchantQrcode"

export default {
  name: "OfflinePaymentStatistics",
  data() {
    return {
      statistics: {},
      qrcodeStatistics: {},
      recentPayments: [],
      failedTransfers: []
    };
  },
  created() {
    this.getStatistics();
    this.getRecentPayments();
    this.getFailedTransfers();
    this.getQrcodeStatistics();
  },
  methods: {
    /** 获取统计信息 */
    getStatistics() {
      getOfflinePaymentStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 获取二维码统计信息 */
    getQrcodeStatistics() {
      getMerchantQrcodeStatistics().then(response => {
        this.qrcodeStatistics = response.data;
      });
    },
    /** 获取最近支付订单 */
    getRecentPayments() {
      const queryParams = {
        pageNum: 1,
        pageSize: 10
      };
      listOfflinePayment(queryParams).then(response => {
        this.recentPayments = response.rows.slice(0, 5);
      });
    },
    /** 获取转账失败订单 */
    getFailedTransfers() {
      const queryParams = {
        pageNum: 1,
        pageSize: 10,
        payStatus: '1',
        transferStatus: '3'
      };
      listOfflinePayment(queryParams).then(response => {
        this.failedTransfers = response.rows.slice(0, 5);
      });
    },
    /** 重试转账 */
    handleRetryTransfer(row) {
      this.$modal.confirm('是否确认重新转账给商家"' + row.merchantName + '"？').then(() => {
        return retryTransfer(row.paymentId);
      }).then(() => {
        this.$modal.msgSuccess("转账操作已提交");
        this.getFailedTransfers();
        this.getStatistics();
      }).catch(() => { });
    },
    /** 查看转账失败订单 */
    handleViewTransferFailed() {
      this.$router.push({
        path: '/offlinePayment/offlinePayment',
        query: {
          payStatus: '1',
          transferStatus: '3'
        }
      });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 18px 0;
  text-align: center;
}

.count {
  font-size: 28px;
  font-weight: bold;
  margin-left: 10px;
}

.statistic-item {
  text-align: center;
  padding: 20px 0;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.statistic-content {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
