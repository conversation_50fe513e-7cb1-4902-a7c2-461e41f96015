# 任务评价功能部署说明

## 功能概述

实现了完整的任务评价系统，包括：

- 发单人对接单人进行 1-5 分评价
- 评价信息展示在接单人履历时间线
- 管理后台评价信息查看和管理
- 支持匿名评价和评价标签
- 评价统计功能

## 部署步骤

### 1. 数据库更新

执行以下 SQL 脚本更新数据库：

```bash
# 执行主要的数据库更新脚本
mysql -u root -p fuguang < fuguang-api/sql/fuguang.sql

# 可选：执行测试脚本验证功能
mysql -u root -p fuguang < fuguang-api/test-evaluation.sql
```

主要更新内容：

- 创建 `task_evaluation` 表
- 添加评价管理相关菜单和权限
- 插入测试数据（可选）

### 2. 后端代码部署

新增的后端文件：

```
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/domain/TaskEvaluation.java
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/mapper/TaskEvaluationMapper.java
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/service/ITaskEvaluationService.java
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/service/impl/TaskEvaluationServiceImpl.java
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/controller/TaskEvaluationController.java
fuguang-api/ruoyi-fuguang/src/main/resources/mapper/fuguang/TaskEvaluationMapper.xml
fuguang-api/ruoyi-app/src/main/java/com/ruoyi/app/controller/AppTaskEvaluationController.java
```

修改的后端文件：

```
fuguang-api/ruoyi-fuguang/src/main/java/com/ruoyi/fuguang/service/impl/AppTaskServiceImpl.java
```

### 3. 前端代码部署

新增的前端文件：

```
fuguang-web/src/api/fuguang/evaluation.js
fuguang-web/src/views/fuguang/evaluation/index.vue
```

### 4. 重启服务

```bash
# 重启后端服务
# 根据你的部署方式重启Spring Boot应用

# 重启前端服务（如果需要）
# 根据你的部署方式重启Vue应用
```

## 功能验证

### 1. 管理后台验证

1. 登录管理后台
2. 检查左侧菜单是否出现"任务评价管理"
3. 点击进入评价管理页面
4. 验证列表查询、搜索、删除等功能

### 2. API 接口验证

#### 管理后台接口：

- `GET /fuguang/evaluation/list` - 查询评价列表
- `GET /fuguang/evaluation/{evaluationId}` - 查询评价详情
- `PUT /fuguang/evaluation` - 修改评价
- `DELETE /fuguang/evaluation/{evaluationIds}` - 删除评价
- `GET /fuguang/evaluation/statistics/{receiverId}` - 获取用户评价统计

#### APP 端接口：

- `POST /app/evaluation/submit` - 提交评价
- `GET /app/evaluation/canEvaluate/{taskId}` - 检查是否可评价
- `GET /app/evaluation/task/{taskId}` - 根据任务 ID 查询评价
- `GET /app/evaluation/receiver/{receiverId}` - 查询用户收到的评价
- `GET /app/evaluation/myEvaluations` - 查询当前用户发出的评价
- `GET /app/evaluation/receivedEvaluations` - 查询当前用户收到的评价
- `GET /app/evaluation/statistics/{receiverId}` - 获取用户评价统计

### 3. 完整流程验证

1. **任务完成**：

   - 接单人完成任务
   - 系统自动为发单人添加评价提醒到时间线

2. **提交评价**：

   - 发单人通过 APP 提交评价（1-5 分）
   - 可选择匿名评价和添加评价标签

3. **评价展示**：

   - 评价信息自动添加到接单人的履历时间线
   - 在个人简介中可查看评价统计

4. **管理后台**：
   - 管理员可查看所有评价信息
   - 支持搜索、筛选、删除等管理操作

## 数据库表结构

### task_evaluation 表

| 字段名             | 类型         | 说明                      |
| ------------------ | ------------ | ------------------------- |
| evaluation_id      | bigint       | 评价 ID（主键）           |
| task_id            | bigint       | 任务 ID                   |
| task_title         | varchar(200) | 任务标题                  |
| publisher_id       | bigint       | 发单人 ID                 |
| publisher_name     | varchar(50)  | 发单人昵称                |
| receiver_id        | bigint       | 接单人 ID                 |
| receiver_name      | varchar(50)  | 接单人昵称                |
| rating             | tinyint      | 评分（1-5 分）            |
| evaluation_content | text         | 评价内容                  |
| evaluation_tags    | varchar(500) | 评价标签（JSON 格式）     |
| is_anonymous       | char(1)      | 是否匿名评价（0 否 1 是） |
| status             | char(1)      | 状态（0 正常 1 隐藏）     |
| create_time        | datetime     | 创建时间                  |
| update_time        | datetime     | 更新时间                  |

## 权限配置

### 菜单权限

- 任务评价管理 (fuguang:evaluation:list)
- 任务评价查询 (fuguang:evaluation:query)
- 任务评价新增 (fuguang:evaluation:add)
- 任务评价修改 (fuguang:evaluation:edit)
- 任务评价删除 (fuguang:evaluation:remove)
- 任务评价导出 (fuguang:evaluation:export)

### 角色分配

默认为管理员角色(role_id=1)分配了所有评价管理权限。

## 注意事项

1. **评价限制**：

   - 只有任务发单人可以评价
   - 只有已完成的任务可以评价
   - 每个任务只能评价一次

2. **数据完整性**：

   - 评价提交时会自动验证任务状态和用户权限
   - 评价信息会同步更新到用户时间线

3. **性能考虑**：

   - 已为常用查询字段添加索引
   - 评价统计查询已优化

4. **扩展性**：
   - 支持评价标签的 JSON 格式存储
   - 预留了匿名评价功能
   - 支持评价状态管理（正常/隐藏）

## 更新内容 (v1.1)

### 修复问题

1. **修复统计各评分等级数量的方法报错**：

   - 修正了 `TaskEvaluationMapper.xml` 中 `selectRatingStatsByReceiverId` 方法的返回类型
   - 更新了 `TaskEvaluationMapper.java` 接口的返回类型声明
   - 添加了必要的 import 语句

2. **增强管理后台功能**：
   - 在任务管理页面添加了"查看评价"按钮（仅对已完成任务显示）
   - 评价管理页面支持通过任务 ID 参数进行筛选
   - 添加了任务 ID 搜索字段

### 新增功能

- 任务管理页面可直接跳转到对应任务的评价详情
- 评价管理页面支持 URL 参数传递，便于从其他页面跳转

## 故障排查

### 常见问题

1. **菜单不显示**：

   - 检查数据库菜单权限是否正确插入
   - 确认用户角色是否有相应权限

2. **API 接口 404**：

   - 确认 Controller 类是否正确扫描
   - 检查 RequestMapping 路径配置

3. **评价提交失败**：

   - 检查任务状态是否为已完成
   - 确认用户是否为任务发单人
   - 验证是否已评价过

4. **时间线不更新**：

   - 检查 AppUserTimelineService 是否正常注入
   - 确认事务是否正确提交

5. **评分统计查询报错**：
   - 确认 `TaskEvaluationMapper.xml` 中的返回类型为 `java.util.HashMap`
   - 检查 `TaskEvaluationMapper.java` 接口是否正确导入 `java.util.Map`

### 日志查看

```bash
# 查看后端日志
tail -f logs/sys-info.log

# 查看错误日志
tail -f logs/sys-error.log
```

## 测试数据

执行 `test-evaluation.sql` 可以创建测试数据：

- 测试任务（ID: 9999）
- 测试评价记录
- 测试时间线事件

测试完成后可以执行清理 SQL 删除测试数据。
