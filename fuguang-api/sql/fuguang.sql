/*
 Navicat Premium Dump SQL

 Source Server         : 浮光壁垒
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : *************:3306
 Source Schema         : fuguang

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 23/08/2025 17:37:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_config
-- ----------------------------
DROP TABLE IF EXISTS `app_config`;
CREATE TABLE `app_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `config_key` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键名',
  `config_value` text COLLATE utf8mb4_general_ci COMMENT '配置键值',
  `config_type` char(1) COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `config_desc` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '配置描述',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `config_image` text COLLATE utf8mb4_general_ci COMMENT '配置图片',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1002 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP配置表';

-- ----------------------------
-- Records of app_config
-- ----------------------------
BEGIN;
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (1, '隐私协议', 'app.privacy.policy', '这里是隐私协议内容...', 'Y', 'APP隐私协议配置', 'admin', '2025-08-18 14:18:13', '', NULL, '隐私协议配置', NULL);
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (2, '用户协议', 'app.user.agreement', '这里是用户协议内容...', 'Y', 'APP用户协议配置', 'admin', '2025-08-18 14:18:13', '', NULL, '用户协议配置', NULL);
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (3, '首页标语', 'app.home.slogan', '链接你我，共创未来', 'Y', 'APP首页标语配置', 'admin', '2025-08-18 14:18:13', '', NULL, '首页标语配置', NULL);
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (4, '客服电话', 'app.service.phone', '************', 'Y', 'APP客服电话配置', 'admin', '2025-08-18 14:18:13', '', NULL, '客服电话配置', NULL);
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (1000, '兴业助农', 'app.agriculture.title', '请问', 'Y', '', 'admin', '2025-08-18 17:26:51', 'admin', '2025-08-18 18:10:13', NULL, 'https://img.js.design/assets/img/66d9798e351574788a4156fe.png#f50b2fdadd1357ef73fb39688e0e3265');
INSERT INTO `app_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `config_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `config_image`) VALUES (1001, '购物专区', 'app.shopping.title', '123', 'Y', '', 'admin', '2025-08-18 17:27:09', 'admin', '2025-08-18 18:10:19', NULL, '/profile/upload/2025/08/18/WechatIMG366_20250818181017A002.png');
COMMIT;

-- ----------------------------
-- Table structure for app_function
-- ----------------------------
DROP TABLE IF EXISTS `app_function`;
CREATE TABLE `app_function` (
  `function_id` bigint NOT NULL AUTO_INCREMENT COMMENT '功能ID',
  `function_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能名称',
  `function_icon` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '功能图标',
  `function_url` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '功能链接',
  `function_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '功能类型（0内部页面 1外部链接）',
  `sort_order` int DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `display_location` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示位置',
  PRIMARY KEY (`function_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=1008 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP功能配置表';

-- ----------------------------
-- Records of app_function
-- ----------------------------
BEGIN;
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1, '我的钱包', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', '/pages/wallet/index', '0', 1, '0', 'admin', '2025-08-18 14:18:13', 'admin', '2025-08-18 20:18:21', '我的钱包功能', '0');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (2, '我的任务', '/profile/upload/2025/08/18/WechatIMG366_20250818183858A002.jpg', '/pages/task/my', '0', 2, '0', 'admin', '2025-08-18 14:18:13', 'admin', '2025-08-18 20:20:56', '我的任务功能', '0');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (3, '实名认证', '/profile/upload/2025/08/18/WechatIMG366_20250818183902A003.jpg', '/pages/auth/index', '0', 3, '0', 'admin', '2025-08-18 14:18:13', 'admin', '2025-08-18 20:18:24', '实名认证功能', '0');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (4, '客服中心', '/profile/upload/2025/08/18/WechatIMG366_20250818183907A004.jpg', '/pages/service/index', '0', 4, '0', 'admin', '2025-08-18 14:18:13', 'admin', '2025-08-18 20:21:00', '客服中心功能', '0');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1000, '个人简介', '/profile/upload/2025/08/18/矩形 1_20250818202122A001.svg', '/page', '0', 1, '0', 'admin', '2025-08-18 20:21:35', '', NULL, NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1001, '地址管理', '/profile/upload/2025/08/18/矩形 1 (1)_20250818202157A002.svg', '/pages/address/list', '0', 2, '0', 'admin', '2025-08-18 20:22:11', 'admin', '2025-08-19 08:53:20', NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1002, '需求日记', '/profile/upload/2025/08/18/矩形 1 (2)_20250818202232A003.svg', '/page', '0', 3, '0', 'admin', '2025-08-18 20:22:41', 'admin', '2025-08-18 20:23:30', NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1003, '订单管理', '/profile/upload/2025/08/18/矩形 1 (3)_20250818202303A004.svg', '/page', '0', 4, '0', 'admin', '2025-08-18 20:23:12', 'admin', '2025-08-18 20:23:40', NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1004, '小黑屋', '/profile/upload/2025/08/18/矩形 1 (4)_20250818202352A005.svg', '/page', '0', 5, '0', 'admin', '2025-08-18 20:24:01', '', NULL, NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1005, '小法庭', '/profile/upload/2025/08/18/矩形 1 (5)_20250818202411A006.svg', '/page', '0', 6, '0', 'admin', '2025-08-18 20:24:18', '', NULL, NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1006, '商家合作', '/profile/upload/2025/08/18/矩形 1 (6)_20250818202430A008.svg', '/page', '0', 7, '0', 'admin', '2025-08-18 20:24:38', '', NULL, NULL, '1');
INSERT INTO `app_function` (`function_id`, `function_name`, `function_icon`, `function_url`, `function_type`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `display_location`) VALUES (1007, '设置', '/profile/upload/2025/08/18/矩形 1 (7)_20250818202451A009.svg', '/pages/settings/index', '0', 8, '0', 'admin', '2025-08-18 20:24:59', 'admin', '2025-08-18 20:53:28', NULL, '1');
COMMIT;

-- ----------------------------
-- Table structure for app_notice
-- ----------------------------
DROP TABLE IF EXISTS `app_notice`;
CREATE TABLE `app_notice` (
  `notice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `notice_title` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知标题',
  `notice_content` text COLLATE utf8mb4_general_ci COMMENT '通知内容',
  `notice_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '通知类型（0系统通知 1活动通知 2任务通知）',
  `notice_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '通知状态（0正常 1关闭）',
  `target_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '目标类型（0全部用户 1指定用户）',
  `target_user_id` bigint DEFAULT NULL COMMENT '目标用户ID',
  `is_read` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否已读（0未读 1已读）',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_notice_type` (`notice_type`),
  KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP通知表';

-- ----------------------------
-- Records of app_notice
-- ----------------------------
BEGIN;
INSERT INTO `app_notice` (`notice_id`, `notice_title`, `notice_content`, `notice_type`, `notice_status`, `target_type`, `target_user_id`, `is_read`, `publish_time`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '系统通知', '今晚凌晨系统将维护', '0', '0', '0', NULL, '0', '2025-08-19 20:34:02', '', '2025-08-19 20:34:02', '', NULL, NULL);
INSERT INTO `app_notice` (`notice_id`, `notice_title`, `notice_content`, `notice_type`, `notice_status`, `target_type`, `target_user_id`, `is_read`, `publish_time`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, '活动通知', '快来注册吧', '1', '0', '0', NULL, '0', '2025-08-19 20:34:02', '', '2025-08-19 20:34:02', '', NULL, NULL);
INSERT INTO `app_notice` (`notice_id`, `notice_title`, `notice_content`, `notice_type`, `notice_status`, `target_type`, `target_user_id`, `is_read`, `publish_time`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, '任务通知', '您有任务需要完成', '2', '0', '0', NULL, '0', '2025-08-19 20:34:02', '', '2025-08-19 20:35:27', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for app_task
-- ----------------------------
DROP TABLE IF EXISTS `app_task`;
CREATE TABLE `app_task` (
  `task_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_title` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务标题',
  `task_desc` text COLLATE utf8mb4_general_ci COMMENT '任务描述',
  `task_amount` decimal(10,2) DEFAULT '0.00' COMMENT '任务金额',
  `task_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '任务类型（0普通任务 1紧急任务）',
  `first_type_id` bigint DEFAULT NULL COMMENT '一级任务类型ID',
  `second_type_id` bigint DEFAULT NULL COMMENT '二级任务类型ID',
  `urgent_level` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '紧急程度（0普通 1紧急 2非常紧急）',
  `task_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '任务状态（0待接取 1进行中 2已完成 3已取消）',
  `publisher_id` bigint NOT NULL COMMENT '发布者ID',
  `publisher_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '发布者昵称',
  `publisher_avatar` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '发布者头像',
  `receiver_id` bigint DEFAULT NULL COMMENT '接收者ID',
  `receiver_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '接收者昵称',
  `task_address` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务地址',
  `longitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '经度',
  `latitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '纬度',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `hot_score` int DEFAULT '0' COMMENT '热度分数',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`),
  KEY `idx_publisher_id` (`publisher_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1007 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP任务表';

-- ----------------------------
-- Records of app_task
-- ----------------------------
BEGIN;
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1000, '帮忙取快递', '帮忙到菜鸟驿站取快递，地址在学校门口', 5.00, '0', NULL, NULL, '0', '0', 1000, '测试用户1', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', NULL, '', '北京市海淀区', '116.3', '39.9', '2025-08-18 14:18:13', '2025-08-19 14:18:13', 3, 0, 'admin', '2025-08-18 14:18:13', '', NULL, '帮忙取快递任务');
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, '代买午餐', '帮忙买一份午餐，麦当劳或肯德基都可以', 10.00, '0', NULL, NULL, '0', '0', 1001, '测试用户2', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', NULL, '', '北京市朝阳区', '116.4', '39.9', '2025-08-18 14:18:13', '2025-08-18 16:18:13', 2, 0, 'admin', '2025-08-18 14:18:13', '', NULL, '代买午餐任务');
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, '测试', '是的发送到发送到', 111111.00, '0', NULL, NULL, '0', '0', 1002, '测试用户', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', NULL, '', '天安门1号', '1111', '1111', '2025-08-19 10:12:56', '2025-08-19 12:12:00', 1, 0, '', '2025-08-19 10:13:28', '', NULL, NULL);
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '是打扫打扫', '啊实打实的', 2222222.00, '0', NULL, NULL, '0', '0', 1002, '测试用户', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', NULL, '', '天安门1号', '1111', '1111', '2025-08-19 10:14:32', '2025-08-19 12:14:00', 0, 0, '', '2025-08-19 10:14:56', '', NULL, NULL);
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, '车位非法所得分', '请问请问请问', 100000.00, '0', NULL, NULL, '0', '0', 1002, '测试用户', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', NULL, '', '天安门1号', '1111', '1111', '2025-08-19 10:30:37', '2025-08-19 13:30:00', 16, 0, '', '2025-08-19 10:32:09', '', NULL, NULL);
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, '测试任务', '任务藐视', 1009.00, '0', 2, 11, '1', '0', 1002, '测试用户', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', 1001, '测试用户2', '天安门1号', '1111', '1111', '2025-08-19 19:08:25', '2025-08-20 17:37:00', 181, 0, '', '2025-08-19 17:38:38', '', '2025-08-19 19:09:41', NULL);
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1006, 'sdgsdfsdfgsd', 'sdfgsdfgsdfgsdfg', 10000.00, '0', 4, 19, '2', '2', 1002, '测试用户', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', 1001, '测试用户2', '天安门1号', '1111', '1111', '2025-08-19 20:46:10', '2025-08-19 22:46:00', 6, 0, '', '2025-08-19 20:46:53', '', '2025-08-19 20:49:14', NULL);
COMMIT;

-- ----------------------------
-- Table structure for app_task_image
-- ----------------------------
DROP TABLE IF EXISTS `app_task_image`;
CREATE TABLE `app_task_image` (
  `image_id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `image_name` varchar(255) DEFAULT NULL COMMENT '图片名称',
  `image_size` bigint DEFAULT NULL COMMENT '图片大小（字节）',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`image_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='APP任务图片表';

-- ----------------------------
-- Records of app_task_image
-- ----------------------------
BEGIN;
INSERT INTO `app_task_image` (`image_id`, `task_id`, `image_url`, `image_name`, `image_size`, `sort_order`, `create_time`) VALUES (2, 1005, 'http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819173818A002.png', 'WechatIMG366.png', NULL, 0, '2025-08-19 19:09:41');
INSERT INTO `app_task_image` (`image_id`, `task_id`, `image_url`, `image_name`, `image_size`, `sort_order`, `create_time`) VALUES (3, 1006, 'http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819204638A001.png', 'WechatIMG366.png', NULL, 0, '2025-08-19 20:46:53');
COMMIT;

-- ----------------------------
-- Table structure for app_task_type
-- ----------------------------
DROP TABLE IF EXISTS `app_task_type`;
CREATE TABLE `app_task_type` (
  `type_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(50) NOT NULL COMMENT '类型名称',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父类型ID（0为一级类型）',
  `type_icon` varchar(255) DEFAULT NULL COMMENT '类型图标',
  `order_num` int DEFAULT '0' COMMENT '排序',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `type_desc` varchar(500) DEFAULT NULL COMMENT '类型描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`type_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='APP任务类型表';

-- ----------------------------
-- Records of app_task_type
-- ----------------------------
BEGIN;
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '生活服务', 0, '/static/icons/life.png', 1, '0', '日常生活相关服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '跑腿代办', 0, '/static/icons/errand.png', 2, '0', '跑腿代办相关服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '技能服务', 0, '/static/icons/skill.png', 3, '0', '专业技能相关服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '搬运装卸', 0, '/static/icons/moving.png', 4, '0', '搬运装卸相关服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '其他服务', 0, '/static/icons/other.png', 5, '0', '其他类型服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '家政清洁', 1, '/static/icons/cleaning.png', 1, '0', '家庭清洁服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '维修安装', 1, '/static/icons/repair.png', 2, '0', '家电维修安装', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '宠物服务', 1, '/static/icons/pet.png', 3, '0', '宠物相关服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '照看陪护', 1, '/static/icons/care.png', 4, '0', '照看陪护服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '代买代购', 2, '/static/icons/shopping.png', 1, '0', '代买代购服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, '文件递送', 2, '/static/icons/delivery.png', 2, '0', '文件递送服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, '排队代办', 2, '/static/icons/queue.png', 3, '0', '排队代办服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, '取送物品', 2, '/static/icons/pickup.png', 4, '0', '取送物品服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (14, '设计制作', 3, '/static/icons/design.png', 1, '0', '设计制作服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (15, '教学培训', 3, '/static/icons/teaching.png', 2, '0', '教学培训服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (16, '翻译服务', 3, '/static/icons/translate.png', 3, '0', '翻译服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (17, '咨询服务', 3, '/static/icons/consult.png', 4, '0', '咨询服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (18, '搬家服务', 4, '/static/icons/move.png', 1, '0', '搬家服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (19, '货物装卸', 4, '/static/icons/loading.png', 2, '0', '货物装卸服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (20, '家具安装', 4, '/static/icons/furniture.png', 3, '0', '家具安装服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (21, '临时帮工', 5, '/static/icons/temp.png', 1, '0', '临时帮工服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (22, '活动协助', 5, '/static/icons/event.png', 2, '0', '活动协助服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
INSERT INTO `app_task_type` (`type_id`, `type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (23, '其他需求', 5, '/static/icons/others.png', 3, '0', '其他需求服务', 'admin', '2025-08-19 09:22:50', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `email` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号码',
  `sex` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密码',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `user_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '用户类型（0普通用户 1商家用户）',
  `auth_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '实名认证状态（0未认证 1已认证）',
  `real_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '真实姓名',
  `id_card` varchar(18) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '身份证号',
  `address` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '地址',
  `longitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '经度',
  `latitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '纬度',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_user_name` (`user_name`),
  KEY `idx_phonenumber` (`phonenumber`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=1007 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP用户信息表';

-- ----------------------------
-- Records of app_user
-- ----------------------------
BEGIN;
INSERT INTO `app_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `user_type`, `auth_status`, `real_name`, `id_card`, `address`, `longitude`, `latitude`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1000, 'appuser1', '测试用户1', '<EMAIL>', '13800138001', '0', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', '$2a$10$7JB720yubVSOfvam/RdTWuH.lkw3g5ewjhKTjOmjbqmKiMILddKpe', '0', '0', '127.0.0.1', '2025-08-18 14:18:13', '0', '0', '', '', '', '', '', 'admin', '2025-08-18 14:18:13', '', NULL, '测试用户1');
INSERT INTO `app_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `user_type`, `auth_status`, `real_name`, `id_card`, `address`, `longitude`, `latitude`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, '12345', '测试用户2', '<EMAIL>', '13800138002', '1', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-08-19 20:48:02', '0', '0', '', '', '', '', '', 'admin', '2025-08-18 14:18:13', '', NULL, '测试用户2');
INSERT INTO `app_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `user_type`, `auth_status`, `real_name`, `id_card`, `address`, `longitude`, `latitude`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, '1234', '测试用户', '', '***********', '0', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-08-19 20:44:18', '0', '0', '', '', '', '', '', '', '2025-08-18 16:46:57', '', '2025-08-19 08:59:35', NULL);
INSERT INTO `app_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `user_type`, `auth_status`, `real_name`, `id_card`, `address`, `longitude`, `latitude`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '123', '123', '', '', '2', '', '$2a$10$TLsGValfHXUS.sXoc8APU.2IjRYEnz9jObGmGyf0yLT6v.WTYu.N.', '0', '2', '127.0.0.1', '2025-08-19 08:09:35', '0', '0', '', '', '', '', '', '', '2025-08-19 08:09:35', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for app_user_profile
-- ----------------------------
DROP TABLE IF EXISTS `app_user_profile`;
CREATE TABLE `app_user_profile` (
  `profile_id` bigint NOT NULL AUTO_INCREMENT COMMENT '简介ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `credit_score` int DEFAULT '100' COMMENT '信用分（默认100分）',
  `task_score` int DEFAULT '0' COMMENT '任务分（完成任务获得）',
  `poverty_relief_badge` char(1) DEFAULT '0' COMMENT '扶贫救援标志（0无 1有）',
  `profile_desc` text COMMENT '个人描述',
  `profile_images` text COMMENT '个人介绍图片（JSON格式存储多张图片路径）',
  `profile_video` varchar(500) DEFAULT '' COMMENT '个人介绍短视频路径',
  `total_tasks` int DEFAULT '0' COMMENT '总任务数',
  `completed_tasks` int DEFAULT '0' COMMENT '完成任务数',
  `success_rate` decimal(5,2) DEFAULT '0.00' COMMENT '任务成功率',
  `total_earnings` decimal(10,2) DEFAULT '0.00' COMMENT '总收益',
  `level` int DEFAULT '1' COMMENT '用户等级',
  `experience` int DEFAULT '0' COMMENT '经验值',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`profile_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_credit_score` (`credit_score`),
  KEY `idx_task_score` (`task_score`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP用户个人简介表';

-- ----------------------------
-- Records of app_user_profile
-- ----------------------------
BEGIN;
INSERT INTO `app_user_profile` (`profile_id`, `user_id`, `credit_score`, `task_score`, `poverty_relief_badge`, `profile_desc`, `profile_images`, `profile_video`, `total_tasks`, `completed_tasks`, `success_rate`, `total_earnings`, `level`, `experience`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1000, 1000, 100, 50, '0', '热心助人的好邻居，乐于帮助他人解决生活中的小困难。', '[]', '', 5, 4, 80.00, 25.00, 2, 150, '0', 'admin', '2025-08-24 10:00:00', '', NULL, NULL);
INSERT INTO `app_user_profile` (`profile_id`, `user_id`, `credit_score`, `task_score`, `poverty_relief_badge`, `profile_desc`, `profile_images`, `profile_video`, `total_tasks`, `completed_tasks`, `success_rate`, `total_earnings`, `level`, `experience`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, 1001, 95, 30, '1', '积极参与社区公益活动，曾参与多次扶贫救援行动。', '[]', '', 3, 3, 100.00, 15.00, 1, 80, '0', 'admin', '2025-08-24 10:00:00', '', NULL, NULL);
INSERT INTO `app_user_profile` (`profile_id`, `user_id`, `credit_score`, `task_score`, `poverty_relief_badge`, `profile_desc`, `profile_images`, `profile_video`, `total_tasks`, `completed_tasks`, `success_rate`, `total_earnings`, `level`, `experience`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, 1002, 105, 80, '0', '专业可靠，服务周到，致力于为大家提供优质的帮助服务。', '[]', '', 8, 7, 87.50, 45.00, 3, 220, '0', 'admin', '2025-08-24 10:00:00', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for app_user_timeline
-- ----------------------------
DROP TABLE IF EXISTS `app_user_timeline`;
CREATE TABLE `app_user_timeline` (
  `timeline_id` bigint NOT NULL AUTO_INCREMENT COMMENT '时间线ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型（register注册 task_complete完成任务 reward获得奖励 level_up升级等）',
  `event_title` varchar(200) NOT NULL COMMENT '事件标题',
  `event_desc` text COMMENT '事件描述',
  `event_data` text COMMENT '事件相关数据（JSON格式）',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `icon` varchar(100) DEFAULT '' COMMENT '事件图标',
  `color` varchar(20) DEFAULT 'primary' COMMENT '事件颜色标识',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1隐藏）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`timeline_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_time` (`event_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP用户履历时间线表';

-- ----------------------------
-- Records of app_user_timeline
-- ----------------------------
BEGIN;
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1000, 1000, 'register', '注册浮光壁垒', '欢迎加入浮光壁垒大家庭！', '{"platform":"app","version":"1.0"}', '2025-08-18 14:18:13', 'user-plus', 'success', '0', 'system', '2025-08-18 14:18:13', '', NULL, NULL);
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, 1000, 'task_complete', '完成任务：帮忙取快递', '成功完成取快递任务，获得5元佣金', '{"taskId":1000,"amount":5.00,"taskTitle":"帮忙取快递"}', '2025-08-18 16:30:00', 'check-circle', 'primary', '0', 'system', '2025-08-18 16:30:00', '', NULL, NULL);
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, 1001, 'register', '注册浮光壁垒', '欢迎加入浮光壁垒大家庭！', '{"platform":"app","version":"1.0"}', '2025-08-18 14:18:13', 'user-plus', 'success', '0', 'system', '2025-08-18 14:18:13', '', NULL, NULL);
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, 1001, 'reward', '获得扶贫救援徽章', '积极参与社区公益活动，获得扶贫救援徽章', '{"badgeType":"poverty_relief","badgeName":"扶贫救援徽章"}', '2025-08-20 10:00:00', 'award', 'warning', '0', 'system', '2025-08-20 10:00:00', '', NULL, NULL);
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, 1002, 'register', '注册浮光壁垒', '欢迎加入浮光壁垒大家庭！', '{"platform":"app","version":"1.0"}', '2025-08-18 16:46:57', 'user-plus', 'success', '0', 'system', '2025-08-18 16:46:57', '', NULL, NULL);
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, 1002, 'level_up', '等级提升', '恭喜您升级到3级！', '{"oldLevel":2,"newLevel":3,"experience":220}', '2025-08-22 15:30:00', 'trophy', 'danger', '0', 'system', '2025-08-22 15:30:00', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- 菜单权限配置 - 个人简介管理
-- ----------------------------

-- 个人简介管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2100, '个人简介管理', 2000, 5, 'profile', 'fuguang/profile/index', '', 1, 0, 'C', '0', '0', 'fuguang:profile:list', 'user', 'admin', sysdate(), '', null, 'APP用户个人简介管理菜单');

-- 个人简介管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2101, '个人简介查询', 2100, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:profile:query', '#', 'admin', sysdate(), '', null, ''),
(2102, '个人简介新增', 2100, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:profile:add', '#', 'admin', sysdate(), '', null, ''),
(2103, '个人简介修改', 2100, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:profile:edit', '#', 'admin', sysdate(), '', null, ''),
(2104, '个人简介删除', 2100, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:profile:remove', '#', 'admin', sysdate(), '', null, ''),
(2105, '个人简介导出', 2100, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:profile:export', '#', 'admin', sysdate(), '', null, '');

-- 履历时间线管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2110, '履历时间线', 2000, 6, 'timeline', 'fuguang/timeline/index', '', 1, 0, 'C', '0', '0', 'fuguang:timeline:list', 'time-range', 'admin', sysdate(), '', null, 'APP用户履历时间线管理菜单');

-- 履历时间线管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2111, '履历时间线查询', 2110, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:timeline:query', '#', 'admin', sysdate(), '', null, ''),
(2112, '履历时间线新增', 2110, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:timeline:add', '#', 'admin', sysdate(), '', null, ''),
(2113, '履历时间线修改', 2110, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:timeline:edit', '#', 'admin', sysdate(), '', null, ''),
(2114, '履历时间线删除', 2110, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:timeline:remove', '#', 'admin', sysdate(), '', null, ''),
(2115, '履历时间线导出', 2110, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:timeline:export', '#', 'admin', sysdate(), '', null, '');

-- 为管理员角色分配新菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 2100), (1, 2101), (1, 2102), (1, 2103), (1, 2104), (1, 2105),
(1, 2110), (1, 2111), (1, 2112), (1, 2113), (1, 2114), (1, 2115);

-- ----------------------------
-- Table structure for app_user_address
-- ----------------------------
DROP TABLE IF EXISTS `app_user_address`;
CREATE TABLE `app_user_address` (
  `address_id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `contact_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(11) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人手机号',
  `contact_sex` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '联系人性别（0男 1女 2未知）',
  `province` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '省份',
  `city` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '城市',
  `district` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '区县',
  `address` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `full_address` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '完整地址',
  `longitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '经度',
  `latitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '纬度',
  `is_default` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否默认地址（0否 1是）',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`address_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP用户地址表';

-- ----------------------------
-- Records of app_user_address
-- ----------------------------
BEGIN;
INSERT INTO `app_user_address` (`address_id`, `user_id`, `contact_name`, `contact_phone`, `contact_sex`, `province`, `city`, `district`, `address`, `full_address`, `longitude`, `latitude`, `is_default`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 1002, 'zhangsan', '***********', '0', '', '', '', '天安门1号', '天安门1号', '1111', '1111', '1', '0', '0', '1002', '2025-08-19 09:01:23', 'admin', '2025-08-19 10:12:46', NULL);
INSERT INTO `app_user_address` (`address_id`, `user_id`, `contact_name`, `contact_phone`, `contact_sex`, `province`, `city`, `district`, `address`, `full_address`, `longitude`, `latitude`, `is_default`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 1002, '历时', '***********', '0', '', '', '', '完毕', '完毕', '', '', '0', '0', '0', '1002', '2025-08-19 09:02:05', '1002', '2025-08-19 09:03:24', NULL);
COMMIT;

-- ----------------------------
-- Table structure for balance_record
-- ----------------------------
DROP TABLE IF EXISTS `balance_record`;
CREATE TABLE `balance_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `change_type` char(1) NOT NULL COMMENT '变动类型（1收入 2支出）',
  `income_type` char(1) DEFAULT NULL COMMENT '收入类型（1任务佣金 2推荐奖励 3其他收入）',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型（task_commission任务佣金 withdraw提现 refund退款等）',
  `business_id` bigint DEFAULT NULL COMMENT '关联业务ID',
  `business_no` varchar(64) DEFAULT NULL COMMENT '关联业务单号',
  `description` varchar(200) NOT NULL COMMENT '变动说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_income_type` (`income_type`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_balance_record_user_time` (`user_id`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='余额变动记录表';

-- ----------------------------
-- Records of balance_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for commission_bill
-- ----------------------------
DROP TABLE IF EXISTS `commission_bill`;
CREATE TABLE `commission_bill` (
  `bill_id` bigint NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `bill_year` int NOT NULL COMMENT '账单年份',
  `bill_month` int NOT NULL COMMENT '账单月份',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `task_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '任务佣金收入',
  `recommend_reward` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐奖励收入',
  `other_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '其他收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现',
  `withdraw_count` int NOT NULL DEFAULT '0' COMMENT '提现次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bill_id`),
  UNIQUE KEY `uk_user_month` (`user_id`,`bill_year`,`bill_month`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_bill_date` (`bill_year`,`bill_month`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_commission_bill_user_time` (`user_id`,`bill_year`,`bill_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金账单表';

-- ----------------------------
-- Records of commission_bill
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) COLLATE utf8mb4_general_ci DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成业务表';

-- ----------------------------
-- Records of gen_table
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) COLLATE utf8mb4_general_ci DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成业务表字段';

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_category
-- ----------------------------
DROP TABLE IF EXISTS `mall_category`;
CREATE TABLE `mall_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `category_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `category_icon` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分类图标',
  `sort_order` int DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分类表';

-- ----------------------------
-- Records of mall_category
-- ----------------------------
BEGIN;
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 0, '数码电器', '', 1, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '数码电器分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 1, '手机通讯', '', 1, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '手机通讯分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, 1, '电脑办公', '', 2, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '电脑办公分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, 0, '服装鞋帽', '', 2, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '服装鞋帽分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, 4, '男装', '', 1, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '男装分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, 4, '女装', '', 2, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '女装分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, 0, '家居生活', '', 3, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '家居生活分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, 7, '家具', '', 1, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '家具分类');
INSERT INTO `mall_category` (`category_id`, `parent_id`, `category_name`, `category_icon`, `sort_order`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, 7, '家电', '', 2, '0', '0', 'admin', '2025-08-20 21:23:35', '', NULL, '家电分类');
COMMIT;

-- ----------------------------
-- Table structure for mall_order
-- ----------------------------
DROP TABLE IF EXISTS `mall_order`;
CREATE TABLE `mall_order` (
  `order_id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `address_id` bigint DEFAULT NULL COMMENT '收货地址ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `order_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '订单状态（0待付款 1已付款 2已发货 3已完成 4已取消 5已退款）',
  `pay_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '支付状态（0未支付 1已支付 2支付失败）',
  `pay_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '发货时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `receiver_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收货人姓名',
  `receiver_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收货人电话',
  `receiver_address` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收货地址',
  `delivery_fee` decimal(10,2) DEFAULT '0.00' COMMENT '配送费',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_address_id` (`address_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单表';

-- ----------------------------
-- Records of mall_order
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_order_item
-- ----------------------------
DROP TABLE IF EXISTS `mall_order_item`;
CREATE TABLE `mall_order_item` (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `spec_id` bigint DEFAULT NULL COMMENT '商品规格ID',
  `product_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `spec_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '规格名称',
  `product_image` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '商品图片',
  `spec_image` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '规格图片',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int NOT NULL COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`item_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_id` (`spec_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单详情表';

-- ----------------------------
-- Records of mall_order_item
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_order_logistics
-- ----------------------------
DROP TABLE IF EXISTS `mall_order_logistics`;
CREATE TABLE `mall_order_logistics` (
  `logistics_id` bigint NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `logistics_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '物流类型（1发货 2退货）',
  `logistics_company` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '物流公司',
  `logistics_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '物流单号',
  `logistics_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '物流状态（0待发货 1已发货 2运输中 3已签收 4异常）',
  `sender_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '发件人姓名',
  `sender_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '发件人电话',
  `sender_address` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '发件地址',
  `receiver_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收件人姓名',
  `receiver_phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收件人电话',
  `receiver_address` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收件地址',
  `send_time` datetime DEFAULT NULL COMMENT '发货时间',
  `receive_time` datetime DEFAULT NULL COMMENT '签收时间',
  `logistics_info` text COLLATE utf8mb4_general_ci COMMENT '物流跟踪信息（JSON格式）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`logistics_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_logistics_status` (`logistics_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单物流表';

-- ----------------------------
-- Records of mall_order_logistics
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_payment
-- ----------------------------
DROP TABLE IF EXISTS `mall_payment`;
CREATE TABLE `mall_payment` (
  `payment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trade_no` (`trade_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付记录表';

-- ----------------------------
-- Records of mall_payment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_product
-- ----------------------------
DROP TABLE IF EXISTS `mall_product`;
CREATE TABLE `mall_product` (
  `product_id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `product_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_desc` text COLLATE utf8mb4_general_ci COMMENT '商品描述',
  `product_image` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '商品主图',
  `product_images` text COLLATE utf8mb4_general_ci COMMENT '商品图片集合（JSON格式）',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '销售价格',
  `stock_quantity` int DEFAULT '0' COMMENT '库存数量',
  `sales_count` int DEFAULT '0' COMMENT '销量',
  `product_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '商品状态（0上架 1下架）',
  `is_hot` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否热门（0否 1是）',
  `is_new` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否新品（0否 1是）',
  `is_recommend` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否推荐（0否 1是）',
  `sort_order` int DEFAULT '0' COMMENT '显示顺序',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`product_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_product_status` (`product_status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_is_recommend` (`is_recommend`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品信息表';

-- ----------------------------
-- Records of mall_product
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mall_product_spec
-- ----------------------------
DROP TABLE IF EXISTS `mall_product_spec`;
CREATE TABLE `mall_product_spec` (
  `spec_id` bigint NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `spec_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称',
  `spec_image` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '规格图片',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售卖价格',
  `supply_price` decimal(10,2) DEFAULT '0.00' COMMENT '供货价格',
  `stock_quantity` int DEFAULT '0' COMMENT '库存数量',
  `spec_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '规格状态（0上架 1下架）',
  `sort_order` int DEFAULT '0' COMMENT '显示顺序',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`spec_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_status` (`spec_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品规格表';

-- ----------------------------
-- Records of mall_product_spec
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for merchant_application
-- ----------------------------
DROP TABLE IF EXISTS `merchant_application`;
CREATE TABLE `merchant_application` (
  `application_id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `business_license` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '营业执照图片地址',
  `shop_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `shop_address` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺地址',
  `shop_longitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '店铺经度',
  `shop_latitude` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '店铺纬度',
  `legal_person` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人姓名',
  `legal_id_card` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人身份证号',
  `legal_phone` varchar(11) COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人联系方式',
  `business_scope` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '经营范围',
  `application_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '申请状态（0待审核 1审核通过 2审核拒绝）',
  `audit_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '审核备注',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_status` (`status`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商家申请表';

-- ----------------------------
-- Records of merchant_application
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for merchant_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `merchant_qrcode`;
CREATE TABLE `merchant_qrcode` (
  `qrcode_id` bigint NOT NULL AUTO_INCREMENT COMMENT '二维码ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `qrcode_content` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码内容',
  `qrcode_url` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二维码图片URL',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`qrcode_id`),
  UNIQUE KEY `uk_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商家二维码信息表';

-- ----------------------------
-- Records of merchant_qrcode
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for offline_payment
-- ----------------------------
DROP TABLE IF EXISTS `offline_payment`;
CREATE TABLE `offline_payment` (
  `payment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `order_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `transfer_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '转账状态（0未转账 1转账中 2转账成功 3转账失败）',
  `transfer_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转账单号',
  `transfer_time` datetime DEFAULT NULL COMMENT '转账时间',
  `transfer_amount` decimal(10,2) DEFAULT '0.00' COMMENT '转账金额',
  `platform_fee` decimal(10,2) DEFAULT '0.00' COMMENT '平台手续费',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_transfer_status` (`transfer_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线下支付订单表';

-- ----------------------------
-- Records of offline_payment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Blob类型的触发器表';

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='日历信息表';

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Cron类型的触发器表';

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='已触发的触发器表';

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务详细信息表';

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) COLLATE utf8mb4_general_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='存储的悲观锁信息表';

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='暂停的触发器表';

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调度器状态表';

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简单触发器的信息表';

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='同步机制的行锁表';

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`),
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触发器详细信息表';

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参数配置表';

-- ----------------------------
-- Records of sys_config
-- ----------------------------
BEGIN;
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'false', 'Y', 'admin', '2025-08-18 14:18:07', 'admin', '2025-08-22 20:18:20', '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 'admin', '2025-08-18 14:18:07', '', NULL, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');
COMMIT;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门表';

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
BEGIN;
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (100, 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL);
COMMIT;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
BEGIN;
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '性别男');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '性别女');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '通知');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '公告');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, 1, '内部页面', '0', 'app_function_type', '', '', 'Y', '0', 'admin', '2025-08-18 18:08:26', '', NULL, '内部页面');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, 2, '外部链接', '1', 'app_function_type', '', '', 'Y', '0', 'admin', '2025-08-18 18:08:26', '', NULL, '外部链接');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, 0, '首页', '0', 'app_display_location', NULL, 'default', 'N', '0', 'admin', '2025-08-18 20:20:40', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, 0, '我的', '1', 'app_display_location', NULL, 'default', 'N', '0', 'admin', '2025-08-18 20:20:47', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典类型表';

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
BEGIN;
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2025-08-18 14:18:07', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, 'APP功能类型', 'app_function_type', '0', 'admin', '2025-08-18 18:08:26', '', NULL, 'APP功能类型列表');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, 'APP功能显示位置', 'app_display_location', '0', 'admin', '2025-08-18 20:20:27', '', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务调度表';

-- ----------------------------
-- Records of sys_job
-- ----------------------------
BEGIN;
INSERT INTO `sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-08-18 14:18:07', '', NULL, '');
INSERT INTO `sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-08-18 14:18:07', '', NULL, '');
INSERT INTO `sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-08-18 14:18:07', '', NULL, '');
COMMIT;

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志信息',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务调度日志表';

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作系统',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=140 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统访问记录';

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
BEGIN;
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 16:15:49');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (101, NULL, '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '* 必须填写', '2025-08-18 16:37:22');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (102, NULL, '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '* 必须填写', '2025-08-18 16:37:24');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (103, NULL, '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '* 必须填写', '2025-08-18 16:37:25');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (104, NULL, '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '* 必须填写', '2025-08-18 16:37:26');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (105, NULL, '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '* 必须填写', '2025-08-18 16:37:59');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (106, '213123123', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '用户不存在/密码错误', '2025-08-18 16:38:35');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (107, '213123123', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '用户自动注册成功', '2025-08-18 16:46:56');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (108, '213123123', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-08-18 16:46:57');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 17:23:03');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '验证码错误', '2025-08-18 17:55:54');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 17:55:58');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '用户不存在/密码错误', '2025-08-18 18:08:34');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '验证码错误', '2025-08-18 18:08:38');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 18:08:41');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 18:38:39');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (116, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '验证码错误', '2025-08-18 19:52:54');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (117, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 19:52:59');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (118, '213123123', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '用户不存在/密码错误', '2025-08-18 20:27:36');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (119, '213123123', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-08-18 20:28:12');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (120, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-18 20:53:14');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (121, '123', '127.0.0.1', '内网IP', 'Chrome Mobile', 'Android 6.x', '0', '用户自动注册成功', '2025-08-19 08:09:35');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (122, '123', '127.0.0.1', '内网IP', 'Chrome Mobile', 'Android 6.x', '0', '登录成功', '2025-08-19 08:09:35');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (123, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '验证码已失效', '2025-08-19 08:34:04');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-19 08:34:09');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (125, '1234', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-08-19 08:59:46');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (126, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-19 10:03:59');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (127, '1234', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-08-19 17:35:17');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (128, '12345', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '0', '登录成功', '2025-08-19 19:01:41');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-19 20:28:19');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (130, '1234', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-08-19 20:44:18');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (131, '12345', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '0', '登录成功', '2025-08-19 20:48:02');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '用户不存在/密码错误', '2025-08-22 20:17:37');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '1', '验证码错误', '2025-08-22 20:17:40');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (134, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-22 20:17:43');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '退出成功', '2025-08-22 21:18:05');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-22 21:19:28');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-22 22:09:03');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-23 13:24:59');
INSERT INTO `sys_logininfor` (`info_id`, `user_name`, `ipaddr`, `login_location`, `browser`, `os`, `status`, `msg`, `login_time`) VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-08-23 17:02:34');
COMMIT;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3070 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单权限表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-08-18 14:18:06', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-08-18 14:18:06', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2025-08-18 14:18:06', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '若依官网', 0, 4, 'http://ruoyi.vip', NULL, '', '', 0, 0, 'M', '0', '0', '', 'guide', 'admin', '2025-08-18 14:18:06', '', NULL, '若依官网地址');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-08-18 14:18:06', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-08-18 14:18:06', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-08-18 14:18:06', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2025-08-18 14:18:06', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2025-08-18 14:18:06', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-08-18 14:18:06', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2025-08-18 14:18:06', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2025-08-18 14:18:06', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2025-08-18 14:18:06', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2025-08-18 14:18:06', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2025-08-18 14:18:06', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2025-08-18 14:18:06', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2025-08-18 14:18:06', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2025-08-18 14:18:06', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2025-08-18 14:18:06', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2025-08-18 14:18:06', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2025-08-18 14:18:06', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2025-08-18 14:18:06', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2025-08-18 14:18:06', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2025-08-18 14:18:06', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2000, '浮光管理', 0, 5, 'fuguang', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-08-18 16:16:40', '', NULL, '浮光APP管理目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2001, 'APP协议管理', 2000, 1, 'agreement', 'fuguang/agreement/index', NULL, '', 1, 0, 'C', '0', '0', 'fuguang:config:list', 'documentation', 'admin', '2025-08-18 16:16:40', 'admin', '2025-08-19 08:41:03', '协议管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2002, '协议查询', 2001, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:config:query', '#', 'admin', '2025-08-18 16:16:40', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2003, '协议新增', 2001, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:config:add', '#', 'admin', '2025-08-18 16:16:40', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2004, '协议修改', 2001, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:config:edit', '#', 'admin', '2025-08-18 16:16:40', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2005, '协议删除', 2001, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:config:remove', '#', 'admin', '2025-08-18 16:16:40', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2006, '协议导出', 2001, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:config:export', '#', 'admin', '2025-08-18 16:16:40', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2007, 'APP功能配置', 2000, 2, 'function', 'fuguang/function/index', NULL, '', 1, 0, 'C', '0', '0', 'fuguang:function:list', 'component', 'admin', '2025-08-18 18:08:25', 'admin', '2025-08-19 08:41:20', 'APP功能配置菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2008, 'APP功能配置查询', 2007, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:function:query', '#', 'admin', '2025-08-18 18:08:26', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2009, 'APP功能配置新增', 2007, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:function:add', '#', 'admin', '2025-08-18 18:08:26', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2010, 'APP功能配置修改', 2007, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:function:edit', '#', 'admin', '2025-08-18 18:08:26', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2011, 'APP功能配置删除', 2007, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:function:remove', '#', 'admin', '2025-08-18 18:08:26', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2012, 'APP功能配置导出', 2007, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'fuguang:function:export', '#', 'admin', '2025-08-18 18:08:26', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2013, 'APP用户管理', 2000, 3, 'appuser', 'fuguang/appuser/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:appuser:list', 'user', 'admin', '2025-08-19 08:33:24', 'admin', '2025-08-19 08:41:25', 'APP用户管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2014, 'APP用户查询', 2013, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:query', '#', 'admin', '2025-08-19 08:33:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2015, 'APP用户新增', 2013, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:add', '#', 'admin', '2025-08-19 08:33:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2016, 'APP用户修改', 2013, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:edit', '#', 'admin', '2025-08-19 08:33:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2017, 'APP用户删除', 2013, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:remove', '#', 'admin', '2025-08-19 08:33:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2018, 'APP用户导出', 2013, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:export', '#', 'admin', '2025-08-19 08:33:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2019, 'APP用户重置密码', 2013, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:appuser:resetPwd', '#', 'admin', '2025-08-19 08:33:53', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2020, 'APP地址管理', 2000, 4, 'address', 'fuguang/address/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:address:list', 'slider', 'admin', '2025-08-19 08:33:59', 'admin', '2025-08-19 08:41:30', '地址管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2021, '地址查询', 2020, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:address:query', '#', 'admin', '2025-08-19 08:33:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2022, '地址新增', 2020, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:address:add', '#', 'admin', '2025-08-19 08:33:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2023, '地址修改', 2020, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:address:edit', '#', 'admin', '2025-08-19 08:33:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2024, '地址删除', 2020, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:address:remove', '#', 'admin', '2025-08-19 08:33:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2025, '地址导出', 2020, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:address:export', '#', 'admin', '2025-08-19 08:33:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2030, '佣金管理', 0, 6, 'commission', NULL, '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', '2025-08-23 17:32:13', '', NULL, '佣金管理目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2031, '用户余额', 2030, 1, 'balance', 'fuguang/balance/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:balance:list', 'wallet', 'admin', '2025-08-23 17:32:13', '', NULL, '用户余额菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2032, '用户余额查询', 2031, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:query', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2033, '用户余额新增', 2031, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:add', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2034, '用户余额修改', 2031, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:edit', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2035, '用户余额删除', 2031, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:remove', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2036, '用户余额导出', 2031, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:export', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2037, '余额调整', 2031, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:adjust', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2038, '余额冻结', 2031, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:freeze', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2039, '余额解冻', 2031, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:unfreeze', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2040, '余额统计', 2031, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:statistics', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2041, '佣金账单', 2030, 2, 'commission', 'fuguang/commission/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:commission:list', 'documentation', 'admin', '2025-08-23 17:32:13', '', NULL, '佣金账单菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2042, '佣金账单查询', 2041, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:query', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2043, '佣金账单新增', 2041, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:add', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2044, '佣金账单修改', 2041, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:edit', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2045, '佣金账单删除', 2041, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:remove', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2046, '佣金账单导出', 2041, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:export', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2047, '佣金统计', 2041, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:statistics', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2048, '提现管理', 2030, 3, 'withdraw', 'fuguang/withdraw/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:withdraw:list', 'money', 'admin', '2025-08-23 17:32:13', '', NULL, '提现管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2049, '提现记录查询', 2048, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:query', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2050, '提现记录导出', 2048, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:export', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2051, '提现审核', 2048, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:audit', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2052, '提现统计', 2048, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:statistics', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2053, '余额记录', 2030, 4, 'balanceRecord', 'fuguang/balanceRecord/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:balanceRecord:list', 'list', 'admin', '2025-08-23 17:32:13', '', NULL, '余额变动记录菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2054, '余额记录查询', 2053, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:query', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2055, '余额记录新增', 2053, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:add', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2056, '余额记录修改', 2053, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:edit', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2057, '余额记录删除', 2053, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:remove', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2058, '余额记录导出', 2053, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:export', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2059, '余额记录统计', 2053, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:statistics', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2060, '佣金统计', 2030, 5, 'statistics', 'fuguang/statistics/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:statistics:overview', 'chart', 'admin', '2025-08-23 17:32:13', '', NULL, '佣金统计菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2061, '系统概览', 2060, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:overview', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2062, '收支趋势', 2060, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:trend', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2063, '余额分布', 2060, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:balance-distribution', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2064, '收入分布', 2060, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:income-distribution', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2065, '提现渠道', 2060, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:withdraw-channel', '#', 'admin', '2025-08-23 17:32:13', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3000, '商城管理', 0, 6, 'mall', NULL, '', '', 1, 0, 'M', '0', '0', '', 'shopping', 'admin', '2025-08-20 21:23:35', '', NULL, '商城管理目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3001, '商品分类', 3000, 1, 'category', 'mall/category/index', '', '', 1, 0, 'C', '0', '0', 'mall:category:list', 'tree', 'admin', '2025-08-20 21:23:35', '', NULL, '商品分类管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3002, '分类查询', 3001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:query', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3003, '分类新增', 3001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:add', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3004, '分类修改', 3001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:edit', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3005, '分类删除', 3001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:remove', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3010, '商品管理', 3000, 2, 'product', 'mall/product/index', '', '', 1, 0, 'C', '0', '0', 'mall:product:list', 'goods', 'admin', '2025-08-20 21:23:36', '', NULL, '商品管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3011, '商品查询', 3010, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:query', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3012, '商品新增', 3010, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:add', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3013, '商品修改', 3010, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:edit', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3014, '商品删除', 3010, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:remove', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3015, '商品导出', 3010, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:export', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3020, '订单管理', 3000, 3, 'order', 'mall/order/index', '', '', 1, 0, 'C', '0', '0', 'mall:order:list', 'list', 'admin', '2025-08-20 21:23:36', '', NULL, '订单管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3021, '订单查询', 3020, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:query', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3022, '订单详情', 3020, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:detail', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3023, '订单发货', 3020, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:delivery', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3024, '订单取消', 3020, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:cancel', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3025, '订单导出', 3020, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:export', '#', 'admin', '2025-08-20 21:23:36', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3030, '商品规格', 3000, 4, 'spec', 'mall/spec/index', '', '', 1, 0, 'C', '0', '0', 'mall:spec:list', 'component', 'admin', '2025-08-22 20:16:57', '', NULL, '商品规格管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3031, '规格查询', 3030, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:query', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3032, '规格新增', 3030, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:add', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3033, '规格修改', 3030, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:edit', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3034, '规格删除', 3030, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:remove', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3035, '规格导出', 3030, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:export', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3040, '物流管理', 3000, 5, 'logistics', 'mall/logistics/index', '', '', 1, 0, 'C', '0', '0', 'mall:logistics:list', 'guide', 'admin', '2025-08-22 20:16:57', '', NULL, '物流管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3041, '物流查询', 3040, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:query', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3042, '物流新增', 3040, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:add', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3043, '物流修改', 3040, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:edit', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3044, '物流删除', 3040, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:remove', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3045, '物流导出', 3040, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:export', '#', 'admin', '2025-08-22 20:16:57', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3046, '创建发货', 3040, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:delivery', '#', 'admin', '2025-08-22 20:16:58', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3047, '状态更新', 3040, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:status', '#', 'admin', '2025-08-22 20:16:58', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3048, '商家申请审核', 2000, 3, 'merchant', 'fuguang/merchant/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchant:list', 'peoples', 'admin', '2025-08-22 22:08:46', '', NULL, '商家申请审核菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3049, '申请查询', 3048, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:query', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3050, '申请新增', 3048, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:add', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3051, '申请修改', 3048, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:edit', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3052, '申请删除', 3048, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:remove', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3053, '申请导出', 3048, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:export', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3054, '申请审核', 3048, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchant:audit', '#', 'admin', '2025-08-22 22:08:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3055, '线下支付管理', 0, 7, 'offlinePayment', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', '2025-08-23 13:43:15', 'admin', '2025-08-23 14:04:23', '线下支付管理目录');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3056, '支付订单管理', 3055, 1, 'offlinePayment', 'fuguang/offlinePayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'shopping', 'admin', '2025-08-23 13:43:15', '', NULL, '线下支付订单管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3057, '订单查询', 3056, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:query', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3058, '订单新增', 3056, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:add', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3059, '订单修改', 3056, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:edit', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3060, '订单删除', 3056, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:remove', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3061, '订单导出', 3056, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:export', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3062, '重新转账', 3056, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:transfer', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3063, '二维码管理', 3055, 2, 'merchantQrcode', 'fuguang/merchantQrcode/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchantQrcode:list', 'server', 'admin', '2025-08-23 13:43:15', 'admin', '2025-08-23 14:04:47', '商家二维码管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3064, '二维码查询', 3063, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:query', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3065, '二维码新增', 3063, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:add', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3066, '二维码修改', 3063, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:edit', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3067, '二维码删除', 3063, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:remove', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3068, '二维码导出', 3063, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:export', '#', 'admin', '2025-08-23 13:43:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3069, '支付统计', 3055, 3, 'statistics', 'fuguang/offlinePayment/statistics', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'chart', 'admin', '2025-08-23 13:43:15', 'admin', '2025-08-23 14:04:28', '线下支付统计页面');
COMMIT;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知公告表';

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
BEGIN;
INSERT INTO `sys_notice` (`notice_id`, `notice_title`, `notice_type`, `notice_content`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2025-08-18 14:18:07', '', NULL, '管理员');
INSERT INTO `sys_notice` (`notice_id`, `notice_title`, `notice_type`, `notice_content`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2025-08-18 14:18:07', '', NULL, '管理员');
COMMIT;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求方式',
  `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志记录';

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
BEGIN;
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (100, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configKey\":\"app.user.agreement\",\"configName\":\"用户协议\",\"configType\":\"Y\",\"configValue\":\"# 用户协议\\n\\n欢迎使用浮光APP！\\n\\n## 1. 服务条款\\n本协议是您与浮光APP之间关于使用浮光APP服务的法律协议。\\n\\n## 2. 用户权利和义务\\n2.1 您有权使用浮光APP提供的各项服务\\n2.2 您应当遵守相关法律法规，不得利用本服务从事违法活动\\n\\n## 3. 服务内容\\n3.1 任务发布与接取服务\\n3.2 用户信息管理服务\\n3.3 其他相关服务\\n\\n## 4. 隐私保护\\n我们重视您的隐私保护，详细内容请参阅《隐私协议》\\n\\n## 5. 免责声明\\n5.1 平台仅提供信息撮合服务\\n5.2 用户自行承担交易风险\\n\\n## 6. 协议修改\\n我们保留随时修改本协议的权利，修改后的协议将在APP内公布。\\n\\n如有疑问，请联系客服：************\",\"params\":{},\"remark\":\"APP用户协议配置\"}', '{\"msg\":\"新增配置\'用户协议\'失败，配置键名已存在\",\"code\":500}', 0, NULL, '2025-08-18 16:18:10', 60);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (101, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configKey\":\"app.user.agreement\",\"configName\":\"用户协议\",\"configType\":\"Y\",\"configValue\":\"# 用户协议\\n\\n欢迎使用浮光APP！\\n\\n## 1. 服务条款\\n本协议是您与浮光APP之间关于使用浮光APP服务的法律协议。\\n\\n## 2. 用户权利和义务\\n2.1 您有权使用浮光APP提供的各项服务\\n2.2 您应当遵守相关法律法规，不得利用本服务从事违法活动\\n\\n## 3. 服务内容\\n3.1 任务发布与接取服务\\n3.2 用户信息管理服务\\n3.3 其他相关服务\\n\\n## 4. 隐私保护\\n我们重视您的隐私保护，详细内容请参阅《隐私协议》\\n\\n## 5. 免责声明\\n5.1 平台仅提供信息撮合服务\\n5.2 用户自行承担交易风险\\n\\n## 6. 协议修改\\n我们保留随时修改本协议的权利，修改后的协议将在APP内公布。\\n\\n如有疑问，请联系客服：************\",\"params\":{},\"remark\":\"APP用户协议配置\"}', '{\"msg\":\"新增配置\'用户协议\'失败，配置键名已存在\",\"code\":500}', 0, NULL, '2025-08-18 16:18:20', 51);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (102, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configKey\":\"app.privacy.policy\",\"configName\":\"隐私协议\",\"configType\":\"Y\",\"configValue\":\"# 隐私协议\\n\\n感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护。\\n\\n## 1. 信息收集\\n我们会收集您在使用浮光APP时提供的信息，包括但不限于：\\n- 注册信息：用户名、密码、手机号、邮箱等\\n- 个人资料：头像、昵称、性别、地址等\\n- 位置信息：用于任务匹配和地理位置服务\\n- 设备信息：设备型号、操作系统版本等\\n\\n## 2. 信息使用\\n我们使用收集的信息用于：\\n- 提供和改进我们的服务\\n- 处理您的任务发布和接取请求\\n- 发送重要通知和服务更新\\n- 保护用户安全和防止欺诈\\n\\n## 3. 信息保护\\n我们采取适当的安全措施保护您的个人信息：\\n- 使用加密技术保护数据传输\\n- 限制员工访问个人信息\\n- 定期审查安全措施\\n- 遵守相关法律法规要求\\n\\n## 4. 信息共享\\n除法律要求外，我们不会向第三方分享您的个人信息。\\n\\n## 5. 您的权利\\n您有权访问、更正、删除您的个人信息。\\n\\n## 6. 联系我们\\n如果您对本隐私政策有任何疑问，请联系我们：\\n- 邮箱：<EMAIL>\\n- 电话：************\",\"params\":{},\"remark\":\"APP隐私协议配置\"}', '{\"msg\":\"新增配置\'隐私协议\'失败，配置键名已存在\",\"code\":500}', 0, NULL, '2025-08-18 16:18:25', 55);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (103, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configKey\":\"app.user.agreement\",\"configName\":\"用户协议\",\"configType\":\"Y\",\"configValue\":\"# 用户协议\\n\\n欢迎使用浮光APP！\\n\\n## 1. 服务条款\\n本协议是您与浮光APP之间关于使用浮光APP服务的法律协议。\\n\\n## 2. 用户权利和义务\\n2.1 您有权使用浮光APP提供的各项服务\\n2.2 您应当遵守相关法律法规，不得利用本服务从事违法活动\\n\\n## 3. 服务内容\\n3.1 任务发布与接取服务\\n3.2 用户信息管理服务\\n3.3 其他相关服务\\n\\n## 4. 隐私保护\\n我们重视您的隐私保护，详细内容请参阅《隐私协议》\\n\\n## 5. 免责声明\\n5.1 平台仅提供信息撮合服务\\n5.2 用户自行承担交易风险\\n\\n## 6. 协议修改\\n我们保留随时修改本协议的权利，修改后的协议将在APP内公布。\\n\\n如有疑问，请联系客服：************\",\"params\":{},\"remark\":\"APP用户协议配置\"}', '{\"msg\":\"新增配置\'用户协议\'失败，配置键名已存在\",\"code\":500}', 0, NULL, '2025-08-18 16:20:10', 75671);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (104, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configId\":1000,\"configKey\":\"app.agriculture.title\",\"configName\":\"兴业助农\",\"configType\":\"Y\",\"configValue\":\"请问\",\"createBy\":\"admin\",\"createTime\":\"2025-08-18 17:26:50\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 17:26:50', 2008);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (105, 'APP配置', 1, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configId\":1001,\"configKey\":\"app.shopping.title\",\"configName\":\"购物专区\",\"configType\":\"Y\",\"configValue\":\"123\",\"createBy\":\"admin\",\"createTime\":\"2025-08-18 17:27:09\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 17:27:09', 137);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (106, 'APP配置', 2, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configDesc\":\"\",\"configId\":1000,\"configImage\":\"/profile/upload/2025/08/18/WechatIMG366_20250818181011A001.jpg\",\"configKey\":\"app.agriculture.title\",\"configName\":\"兴业助农\",\"configType\":\"Y\",\"configValue\":\"请问\",\"createBy\":\"admin\",\"createTime\":\"2025-08-18 17:26:51\",\"params\":{},\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:10:13\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:10:13', 158);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (107, 'APP配置', 2, 'com.ruoyi.web.controller.fuguang.AppConfigManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/config', '127.0.0.1', '内网IP', '{\"configDesc\":\"\",\"configId\":1001,\"configImage\":\"/profile/upload/2025/08/18/WechatIMG366_20250818181017A002.png\",\"configKey\":\"app.shopping.title\",\"configName\":\"购物专区\",\"configType\":\"Y\",\"configValue\":\"123\",\"createBy\":\"admin\",\"createTime\":\"2025-08-18 17:27:09\",\"params\":{},\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:10:19\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:10:19', 126);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (108, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"functionId\":1,\"functionName\":\"我的钱包\",\"functionType\":\"0\",\"functionUrl\":\"/pages/wallet/index\",\"params\":{},\"remark\":\"我的钱包功能\",\"sortOrder\":1,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:38:52\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:38:53', 99);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (109, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183858A002.jpg\",\"functionId\":2,\"functionName\":\"我的任务\",\"functionType\":\"0\",\"functionUrl\":\"/pages/task/my\",\"params\":{},\"remark\":\"我的任务功能\",\"sortOrder\":2,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:38:58\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:38:58', 86);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (110, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183902A003.jpg\",\"functionId\":3,\"functionName\":\"实名认证\",\"functionType\":\"0\",\"functionUrl\":\"/pages/auth/index\",\"params\":{},\"remark\":\"实名认证功能\",\"sortOrder\":3,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:39:03\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:39:03', 88);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (111, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183907A004.jpg\",\"functionId\":4,\"functionName\":\"客服中心\",\"functionType\":\"0\",\"functionUrl\":\"/pages/service/index\",\"params\":{},\"remark\":\"客服中心功能\",\"sortOrder\":4,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 18:39:08\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 18:39:08', 88);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (112, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"functionId\":1,\"functionName\":\"我的钱包\",\"functionType\":\"0\",\"functionUrl\":\"/pages/wallet/index\",\"params\":{},\"remark\":\"我的钱包功能\",\"sortOrder\":1,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:18:09\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:18:09', 111);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (113, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183902A003.jpg\",\"functionId\":3,\"functionName\":\"实名认证\",\"functionType\":\"0\",\"functionUrl\":\"/pages/auth/index\",\"params\":{},\"remark\":\"实名认证功能\",\"sortOrder\":3,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:18:13\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:18:14', 85);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (114, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"functionId\":1,\"functionName\":\"我的钱包\",\"functionType\":\"0\",\"functionUrl\":\"/pages/wallet/index\",\"params\":{},\"remark\":\"我的钱包功能\",\"sortOrder\":1,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:18:17\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:18:17', 87);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (115, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"functionId\":1,\"functionName\":\"我的钱包\",\"functionType\":\"0\",\"functionUrl\":\"/pages/wallet/index\",\"params\":{},\"remark\":\"我的钱包功能\",\"sortOrder\":1,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:18:21\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:18:21', 103);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (116, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183902A003.jpg\",\"functionId\":3,\"functionName\":\"实名认证\",\"functionType\":\"0\",\"functionUrl\":\"/pages/auth/index\",\"params\":{},\"remark\":\"实名认证功能\",\"sortOrder\":3,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:18:23\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:18:24', 80);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (117, '字典类型', 1, 'com.ruoyi.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"APP功能显示位置\",\"dictType\":\"app_display_location\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:20:27', 169);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (118, '字典数据', 1, 'com.ruoyi.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"首页\",\"dictSort\":0,\"dictType\":\"app_display_location\",\"dictValue\":\"0\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:20:40', 165);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (119, '字典数据', 1, 'com.ruoyi.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"我的\",\"dictSort\":0,\"dictType\":\"app_display_location\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:20:47', 177);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (120, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183858A002.jpg\",\"functionId\":2,\"functionName\":\"我的任务\",\"functionType\":\"0\",\"functionUrl\":\"/pages/task/my\",\"params\":{},\"remark\":\"我的任务功能\",\"sortOrder\":2,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:20:55\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:20:55', 90);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (121, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:13\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183907A004.jpg\",\"functionId\":4,\"functionName\":\"客服中心\",\"functionType\":\"0\",\"functionUrl\":\"/pages/service/index\",\"params\":{},\"remark\":\"客服中心功能\",\"sortOrder\":4,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:20:59\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:21:00', 89);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (122, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:21:34\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1_20250818202122A001.svg\",\"functionId\":1000,\"functionName\":\"个人简介\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":1,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:21:35', 94);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (123, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:22:11\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (1)_20250818202157A002.svg\",\"functionId\":1001,\"functionName\":\"地址管理\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":2,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:22:11', 91);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (124, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:22:40\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (2)_20250818202232A003.svg\",\"functionId\":1002,\"functionName\":\"需求日记\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":3,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:22:40', 90);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (125, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:23:11\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (3)_20250818202303A004.svg\",\"functionId\":1003,\"functionName\":\"订单管理\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":3,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:23:12', 91);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (126, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:22:41\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (2)_20250818202232A003.svg\",\"functionId\":1002,\"functionName\":\"需求日记\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":3,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:23:29\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:23:30', 99);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (127, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:23:12\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (3)_20250818202303A004.svg\",\"functionId\":1003,\"functionName\":\"订单管理\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":4,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:23:39\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:23:39', 91);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (128, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:00\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (4)_20250818202352A005.svg\",\"functionId\":1004,\"functionName\":\"小黑屋\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":5,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:24:00', 89);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (129, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:18\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (5)_20250818202411A006.svg\",\"functionId\":1005,\"functionName\":\"小法庭\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":6,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:24:18', 89);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (130, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:38\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (6)_20250818202430A008.svg\",\"functionId\":1006,\"functionName\":\"商家合作\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":7,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:24:38', 89);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (131, 'APP功能配置', 1, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.add()', 'POST', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:58\",\"displayLocation\":\"0\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (7)_20250818202451A009.svg\",\"functionId\":1007,\"functionName\":\"设置\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":8,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:24:58', 80);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (132, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:59\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (7)_20250818202451A009.svg\",\"functionId\":1007,\"functionName\":\"设置\",\"functionType\":\"0\",\"functionUrl\":\"/page\",\"params\":{},\"sortOrder\":8,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:25:28\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:25:28', 80);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (133, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:24:59\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (7)_20250818202451A009.svg\",\"functionId\":1007,\"functionName\":\"设置\",\"functionType\":\"0\",\"functionUrl\":\"/pages/settings/index\",\"params\":{},\"sortOrder\":8,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-18 20:53:27\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-18 20:53:28', 105);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (134, 'APP用户', 3, 'com.ruoyi.web.controller.fuguang.AppUserManageController.remove()', 'DELETE', 1, 'admin', '研发部门', '/fuguang/appuser/1003', '127.0.0.1', '内网IP', '[1003]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:39:32', 111);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (135, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/address/index\",\"createTime\":\"2025-08-19 08:33:59\",\"icon\":\"slider\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2020,\"menuName\":\"地址管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"address\",\"perms\":\"fuguang:address:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:40:56', 127);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (136, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/agreement/index\",\"createTime\":\"2025-08-18 16:16:40\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"APP协议管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"agreement\",\"perms\":\"fuguang:config:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:41:03', 145);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (137, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/address/index\",\"createTime\":\"2025-08-19 08:33:59\",\"icon\":\"slider\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2020,\"menuName\":\"APP地址管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"address\",\"perms\":\"fuguang:address:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:41:09', 145);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (138, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/function/index\",\"createTime\":\"2025-08-18 18:08:25\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"APP功能配置\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"function\",\"perms\":\"fuguang:function:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:41:20', 138);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (139, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/appuser/index\",\"createTime\":\"2025-08-19 08:33:24\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2013,\"menuName\":\"APP用户管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"appuser\",\"perms\":\"fuguang:appuser:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:41:25', 121);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (140, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/address/index\",\"createTime\":\"2025-08-19 08:33:59\",\"icon\":\"slider\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2020,\"menuName\":\"APP地址管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"address\",\"perms\":\"fuguang:address:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:41:30', 129);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (141, 'APP用户', 2, 'com.ruoyi.web.controller.fuguang.AppUserManageController.resetPwd()', 'PUT', 1, 'admin', '研发部门', '/fuguang/appuser/resetPwd', '127.0.0.1', '内网IP', '{\"params\":{},\"userId\":1002,\"userName\":\"213123123\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:45:57', 173);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (142, 'APP用户', 2, 'com.ruoyi.web.controller.fuguang.AppUserManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/appuser', '127.0.0.1', '内网IP', '{\"address\":\"\",\"authStatus\":\"0\",\"avatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"createBy\":\"\",\"createTime\":\"2025-08-18 16:46:57\",\"delFlag\":\"0\",\"email\":\"\",\"idCard\":\"\",\"latitude\":\"\",\"loginDate\":\"2025-08-18 20:28:13\",\"loginIp\":\"127.0.0.1\",\"longitude\":\"\",\"nickName\":\"测试用户\",\"params\":{},\"phonenumber\":\"***********\",\"realName\":\"\",\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"\",\"updateTime\":\"2025-08-19 08:46:25\",\"userId\":1002,\"userName\":\"ceshi\",\"userType\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:46:25', 257);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (143, 'APP功能配置', 2, 'com.ruoyi.web.controller.fuguang.AppFunctionManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/function', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-08-18 20:22:11\",\"displayLocation\":\"1\",\"functionIcon\":\"/profile/upload/2025/08/18/矩形 1 (1)_20250818202157A002.svg\",\"functionId\":1001,\"functionName\":\"地址管理\",\"functionType\":\"0\",\"functionUrl\":\"/pages/address/list\",\"params\":{},\"sortOrder\":2,\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-19 08:53:19\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:53:20', 113);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (144, 'APP用户', 2, 'com.ruoyi.web.controller.fuguang.AppUserManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/appuser', '127.0.0.1', '内网IP', '{\"address\":\"\",\"authStatus\":\"0\",\"avatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"createBy\":\"\",\"createTime\":\"2025-08-18 16:46:57\",\"delFlag\":\"0\",\"email\":\"\",\"idCard\":\"\",\"latitude\":\"\",\"loginDate\":\"2025-08-18 20:28:13\",\"loginIp\":\"127.0.0.1\",\"longitude\":\"\",\"nickName\":\"测试用户\",\"params\":{},\"phonenumber\":\"***********\",\"realName\":\"\",\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"\",\"updateTime\":\"2025-08-19 08:59:24\",\"userId\":1002,\"userName\":\"123\",\"userType\":\"0\"}', NULL, 1, '\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'123\' for key \'app_user.uk_user_name\'\n### The error may exist in file [/Users/<USER>/浮光壁垒/project/fuguang-api/ruoyi-fuguang/target/classes/mapper/fuguang/AppUserMapper.xml]\n### The error may involve com.ruoyi.fuguang.mapper.AppUserMapper.updateAppUser-Inline\n### The error occurred while setting parameters\n### SQL: update app_user          SET user_name = ?,             nick_name = ?,             email = ?,             phonenumber = ?,             sex = ?,             avatar = ?,             password = ?,             status = ?,             del_flag = ?,             login_ip = ?,             login_date = ?,             user_type = ?,             auth_status = ?,             real_name = ?,             id_card = ?,             address = ?,             longitude = ?,             latitude = ?,             update_by = ?,             update_time = ?          where user_id = ?\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'123\' for key \'app_user.uk_user_name\'\n; Duplicate entry \'123\' for key \'app_user.uk_user_name\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'123\' for key \'app_user.uk_user_name\'', '2025-08-19 08:59:24', 267);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (145, 'APP用户', 2, 'com.ruoyi.web.controller.fuguang.AppUserManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/appuser', '127.0.0.1', '内网IP', '{\"address\":\"\",\"authStatus\":\"0\",\"avatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"createBy\":\"\",\"createTime\":\"2025-08-18 16:46:57\",\"delFlag\":\"0\",\"email\":\"\",\"idCard\":\"\",\"latitude\":\"\",\"loginDate\":\"2025-08-18 20:28:13\",\"loginIp\":\"127.0.0.1\",\"longitude\":\"\",\"nickName\":\"测试用户\",\"params\":{},\"phonenumber\":\"***********\",\"realName\":\"\",\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"\",\"updateTime\":\"2025-08-19 08:59:34\",\"userId\":1002,\"userName\":\"1234\",\"userType\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 08:59:35', 236);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (146, 'APP用户地址', 1, 'com.ruoyi.app.controller.AppUserAddressController.add()', 'POST', 1, '1234', NULL, '/app/address', '127.0.0.1', '内网IP', '{\"address\":\"天安门1号\",\"addressId\":1,\"city\":\"\",\"contactName\":\"zhangsan\",\"contactPhone\":\"***********\",\"contactSex\":\"0\",\"createBy\":\"1002\",\"createTime\":\"2025-08-19 09:01:23\",\"district\":\"\",\"fullAddress\":\"天安门1号\",\"isDefault\":\"1\",\"latitude\":\"\",\"longitude\":\"\",\"params\":{},\"province\":\"\",\"userId\":1002}', '{\"msg\":\"新增成功\",\"code\":200}', 0, NULL, '2025-08-19 09:01:23', 436);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (147, 'APP用户地址', 1, 'com.ruoyi.app.controller.AppUserAddressController.add()', 'POST', 1, '1234', NULL, '/app/address', '127.0.0.1', '内网IP', '{\"address\":\"完毕\",\"addressId\":2,\"city\":\"\",\"contactName\":\"历时\",\"contactPhone\":\"***********\",\"contactSex\":\"0\",\"createBy\":\"1002\",\"createTime\":\"2025-08-19 09:02:04\",\"district\":\"\",\"fullAddress\":\"完毕\",\"isDefault\":\"1\",\"latitude\":\"\",\"longitude\":\"\",\"params\":{},\"province\":\"\",\"userId\":1002}', '{\"msg\":\"新增成功\",\"code\":200}', 0, NULL, '2025-08-19 09:02:05', 338);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (148, 'APP用户地址', 2, 'com.ruoyi.web.controller.fuguang.AppUserAddressManageController.setDefault()', 'PUT', 1, 'admin', '研发部门', '/fuguang/address/setDefault', '127.0.0.1', '内网IP', '{\"addressId\":1,\"params\":{},\"userId\":1002}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 09:02:16', 298);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (149, 'APP用户地址', 2, 'com.ruoyi.web.controller.fuguang.AppUserAddressManageController.setDefault()', 'PUT', 1, 'admin', '研发部门', '/fuguang/address/setDefault', '127.0.0.1', '内网IP', '{\"addressId\":2,\"params\":{},\"userId\":1002}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 09:02:24', 276);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (150, 'APP用户地址', 2, 'com.ruoyi.web.controller.fuguang.AppUserAddressManageController.setDefault()', 'PUT', 1, 'admin', '研发部门', '/fuguang/address/setDefault', '127.0.0.1', '内网IP', '{\"addressId\":1,\"params\":{},\"userId\":1002}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 09:02:35', 301);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (151, 'APP用户地址', 2, 'com.ruoyi.app.controller.AppUserAddressController.edit()', 'PUT', 1, '1234', NULL, '/app/address', '127.0.0.1', '内网IP', '{\"address\":\"完毕\",\"addressId\":2,\"city\":\"\",\"contactName\":\"历时\",\"contactPhone\":\"***********\",\"contactSex\":\"0\",\"createBy\":\"1002\",\"createTime\":\"2025-08-19 09:02:05\",\"delFlag\":\"0\",\"district\":\"\",\"fullAddress\":\"完毕\",\"isDefault\":\"0\",\"latitude\":\"\",\"longitude\":\"\",\"params\":{},\"province\":\"\",\"status\":\"0\",\"updateBy\":\"1002\",\"updateTime\":\"2025-08-19 09:03:23\",\"userId\":1002,\"userNickName\":\"测试用户\",\"userPhone\":\"***********\"}', '{\"msg\":\"修改成功\",\"code\":200}', 0, NULL, '2025-08-19 09:03:24', 283);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (152, 'APP用户地址', 2, 'com.ruoyi.web.controller.fuguang.AppUserAddressManageController.edit()', 'PUT', 1, 'admin', '研发部门', '/fuguang/address', '127.0.0.1', '内网IP', '{\"address\":\"天安门1号\",\"addressId\":1,\"city\":\"\",\"contactName\":\"zhangsan\",\"contactPhone\":\"***********\",\"contactSex\":\"0\",\"createBy\":\"1002\",\"createTime\":\"2025-08-19 09:01:23\",\"delFlag\":\"0\",\"district\":\"\",\"fullAddress\":\"天安门1号\",\"isDefault\":\"1\",\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"province\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"updateTime\":\"2025-08-19 10:12:45\",\"userId\":1002,\"userNickName\":\"测试用户\",\"userPhone\":\"***********\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 10:12:46', 397);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (153, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 10:13:27\",\"endTime\":\"2025-08-19 12:12:00\",\"firstTypeId\":1,\"hotScore\":0,\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":8,\"startTime\":\"2025-08-19 10:12:56\",\"taskAddress\":\"天安门1号\",\"taskAmount\":111111,\"taskDesc\":\"是的发送到发送到\",\"taskId\":1002,\"taskStatus\":\"0\",\"taskTitle\":\"测试\",\"taskType\":\"0\",\"urgentLevel\":\"普通\",\"viewCount\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 10:13:28', 150);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (154, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 10:14:56\",\"endTime\":\"2025-08-19 12:14:00\",\"firstTypeId\":1,\"hotScore\":0,\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":6,\"startTime\":\"2025-08-19 10:14:31\",\"taskAddress\":\"天安门1号\",\"taskAmount\":2222222,\"taskDesc\":\"啊实打实的\",\"taskId\":1003,\"taskStatus\":\"0\",\"taskTitle\":\"是打扫打扫\",\"taskType\":\"0\",\"urgentLevel\":\"非常紧急\",\"viewCount\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 10:14:56', 121);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (155, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 10:32:09\",\"endTime\":\"2025-08-19 13:30:00\",\"firstTypeId\":1,\"hotScore\":0,\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":6,\"startTime\":\"2025-08-19 10:30:36\",\"taskAddress\":\"天安门1号\",\"taskAmount\":100000,\"taskDesc\":\"请问请问请问\",\"taskId\":1004,\"taskStatus\":\"0\",\"taskTitle\":\"车位非法所得分\",\"taskType\":\"0\",\"urgentLevel\":\"非常紧急\",\"viewCount\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 10:32:09', 176);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (156, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 17:36:17\",\"endTime\":\"2025-08-21 18:35:00\",\"firstTypeId\":1,\"hotScore\":0,\"images\":[{\"url\":\"http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819173606A001.jpg\",\"name\":\"WechatIMG366.jpg\"}],\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":6,\"startTime\":\"2025-08-19 17:35:42\",\"taskAddress\":\"天安门1号\",\"taskAmount\":10000,\"taskDesc\":\"请问请问请问请问\",\"taskStatus\":\"0\",\"taskTitle\":\"测试\",\"taskType\":\"0\",\"urgentLevel\":\"非常紧急\",\"viewCount\":0}', NULL, 1, '\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1\n### The error may exist in file [/Users/<USER>/浮光壁垒/project/fuguang-api/ruoyi-fuguang/target/classes/mapper/fuguang/AppTaskMapper.xml]\n### The error may involve com.ruoyi.fuguang.mapper.AppTaskMapper.insertAppTask-Inline\n### The error occurred while setting parameters\n### SQL: insert into app_task          ( task_title,             task_desc,             task_amount,             task_status,             task_type,             first_type_id,             second_type_id,             urgent_level,             publisher_id,             publisher_name,             publisher_avatar,                                       task_address,             longitude,             latitude,             start_time,             end_time,             view_count,             hot_score,                          create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ? )\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1\n; Data truncation: Data too long for column \'urgent_level\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1', '2025-08-19 17:36:17', 272);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (157, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 17:37:51\",\"endTime\":\"2025-08-21 18:35:00\",\"firstTypeId\":1,\"hotScore\":0,\"images\":[{\"url\":\"http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819173606A001.jpg\",\"name\":\"WechatIMG366.jpg\"}],\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":6,\"startTime\":\"2025-08-19 17:35:42\",\"taskAddress\":\"天安门1号\",\"taskAmount\":10000,\"taskDesc\":\"请问请问请问请问\",\"taskStatus\":\"0\",\"taskTitle\":\"测试\",\"taskType\":\"0\",\"urgentLevel\":\"非常紧急\",\"viewCount\":0}', NULL, 1, '\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1\n### The error may exist in file [/Users/<USER>/浮光壁垒/project/fuguang-api/ruoyi-fuguang/target/classes/mapper/fuguang/AppTaskMapper.xml]\n### The error may involve com.ruoyi.fuguang.mapper.AppTaskMapper.insertAppTask-Inline\n### The error occurred while setting parameters\n### SQL: insert into app_task          ( task_title,             task_desc,             task_amount,             task_status,             task_type,             first_type_id,             second_type_id,             urgent_level,             publisher_id,             publisher_name,             publisher_avatar,                                       task_address,             longitude,             latitude,             start_time,             end_time,             view_count,             hot_score,                          create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ? )\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1\n; Data truncation: Data too long for column \'urgent_level\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'urgent_level\' at row 1', '2025-08-19 17:37:52', 307);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (158, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 17:38:38\",\"endTime\":\"2025-08-21 17:37:00\",\"firstTypeId\":1,\"hotScore\":0,\"images\":[{\"url\":\"http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819173818A002.png\",\"name\":\"WechatIMG366.png\"}],\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":6,\"startTime\":\"2025-08-19 17:37:54\",\"taskAddress\":\"天安门1号\",\"taskAmount\":1000,\"taskDesc\":\"dfsgsdfgsdfgsdfg\",\"taskId\":1005,\"taskStatus\":\"0\",\"taskTitle\":\"asdasdasdasd\",\"taskType\":\"0\",\"urgentLevel\":\"2\",\"viewCount\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 17:38:38', 358);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (159, '取消任务', 2, 'com.ruoyi.app.controller.AppTaskController.cancelTask()', 'POST', 1, '1234', NULL, '/app/task/cancel/1005', '127.0.0.1', '内网IP', '1005', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 18:57:33', 194);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (160, '接取任务', 2, 'com.ruoyi.app.controller.AppTaskController.acceptTask()', 'POST', 1, '12345', NULL, '/app/task/accept/1005', '127.0.0.1', '内网IP', '1005', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 19:01:55', 199);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (161, '完成任务', 2, 'com.ruoyi.app.controller.AppTaskController.completeTask()', 'POST', 1, '12345', NULL, '/app/task/complete/1005', '127.0.0.1', '内网IP', '1005', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 19:02:23', 160);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (162, 'APP任务', 2, 'com.ruoyi.app.controller.AppTaskController.edit()', 'PUT', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"endTime\":\"2025-08-20 17:37:00\",\"firstTypeId\":2,\"images\":[{\"url\":\"http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819173818A002.png\",\"name\":\"WechatIMG366.png\"}],\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"secondTypeId\":11,\"startTime\":\"2025-08-19 19:08:24\",\"taskAddress\":\"天安门1号\",\"taskAmount\":1009,\"taskDesc\":\"任务藐视\",\"taskId\":1005,\"taskTitle\":\"测试任务\",\"taskType\":\"0\",\"updateTime\":\"2025-08-19 19:09:41\",\"urgentLevel\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 19:09:41', 538);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (163, 'APP任务', 1, 'com.ruoyi.app.controller.AppTaskController.add()', 'POST', 1, '1234', NULL, '/app/task', '127.0.0.1', '内网IP', '{\"createTime\":\"2025-08-19 20:46:52\",\"endTime\":\"2025-08-19 22:46:00\",\"firstTypeId\":4,\"hotScore\":0,\"images\":[{\"url\":\"http://localhost:8888/profile/upload/2025/08/19/WechatIMG366_20250819204638A001.png\",\"name\":\"WechatIMG366.png\"}],\"latitude\":\"1111\",\"longitude\":\"1111\",\"params\":{},\"publisherAvatar\":\"/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg\",\"publisherId\":1002,\"publisherName\":\"测试用户\",\"secondTypeId\":19,\"startTime\":\"2025-08-19 20:46:09\",\"taskAddress\":\"天安门1号\",\"taskAmount\":10000,\"taskDesc\":\"sdfgsdfgsdfgsdfg\",\"taskId\":1006,\"taskStatus\":\"0\",\"taskTitle\":\"sdgsdfsdfgsd\",\"taskType\":\"0\",\"urgentLevel\":\"2\",\"viewCount\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 20:46:52', 382);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (164, '接取任务', 2, 'com.ruoyi.app.controller.AppTaskController.acceptTask()', 'POST', 1, '12345', NULL, '/app/task/accept/1006', '127.0.0.1', '内网IP', '1006', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 20:48:52', 210);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (165, '完成任务', 2, 'com.ruoyi.app.controller.AppTaskController.completeTask()', 'POST', 1, '12345', NULL, '/app/task/complete/1006', '127.0.0.1', '内网IP', '1006', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-19 20:49:14', 170);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (166, '参数管理', 2, 'com.ruoyi.web.controller.system.SysConfigController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":4,\"configKey\":\"sys.account.captchaEnabled\",\"configName\":\"账号自助-验证码开关\",\"configType\":\"Y\",\"configValue\":\"false\",\"createBy\":\"admin\",\"createTime\":\"2025-08-18 14:18:07\",\"params\":{},\"remark\":\"是否开启验证码功能（true开启，false关闭）\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-22 20:18:20', 206);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (167, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-08-23 13:43:15\",\"icon\":\"money\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3055,\"menuName\":\"线下支付管理\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"offlinePayment\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-23 14:03:25', 131);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (168, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/offlinePayment/statistics\",\"createTime\":\"2025-08-23 13:24:13\",\"icon\":\"chart\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2044,\"menuName\":\"支付统计\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":3055,\"path\":\"statistics\",\"perms\":\"fuguang:offlinePayment:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"修改菜单\'支付统计\'失败，菜单名称已存在\",\"code\":500}', 0, NULL, '2025-08-23 14:04:08', 48);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (169, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/2044', '127.0.0.1', '内网IP', '2044', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-23 14:04:14', 169);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (170, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-08-23 13:43:15\",\"icon\":\"money\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3055,\"menuName\":\"线下支付管理\",\"menuType\":\"M\",\"orderNum\":7,\"params\":{},\"parentId\":0,\"path\":\"offlinePayment\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-23 14:04:24', 134);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (171, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/offlinePayment/statistics\",\"createTime\":\"2025-08-23 13:43:15\",\"icon\":\"chart\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3069,\"menuName\":\"支付统计\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":3055,\"path\":\"statistics\",\"perms\":\"fuguang:offlinePayment:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-23 14:04:28', 137);
INSERT INTO `sys_oper_log` (`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (172, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"fuguang/merchantQrcode/index\",\"createTime\":\"2025-08-23 13:43:15\",\"icon\":\"server\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3063,\"menuName\":\"二维码管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3055,\"path\":\"merchantQrcode\",\"perms\":\"fuguang:merchantQrcode:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-08-23 14:04:47', 129);
COMMIT;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='岗位信息表';

-- ----------------------------
-- Records of sys_post
-- ----------------------------
BEGIN;
INSERT INTO `sys_post` (`post_id`, `post_code`, `post_name`, `post_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_post` (`post_id`, `post_code`, `post_name`, `post_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_post` (`post_id`, `post_code`, `post_name`, `post_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2025-08-18 14:18:06', '', NULL, '');
INSERT INTO `sys_post` (`post_id`, `post_code`, `post_name`, `post_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2025-08-18 14:18:06', '', NULL, '');
COMMIT;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色信息表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL, '超级管理员');
INSERT INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2025-08-18 14:18:06', '', NULL, '普通角色');
COMMIT;

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色和部门关联表';

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
BEGIN;
INSERT INTO `sys_role_dept` (`role_id`, `dept_id`) VALUES (2, 100);
INSERT INTO `sys_role_dept` (`role_id`, `dept_id`) VALUES (2, 101);
INSERT INTO `sys_role_dept` (`role_id`, `dept_id`) VALUES (2, 105);
COMMIT;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色和菜单关联表';

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3030);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3031);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3032);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3033);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3034);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3035);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3040);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3041);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3042);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3043);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3044);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3045);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3046);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (1, 3047);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 2);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 3);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 4);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 100);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 101);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 102);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 103);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 104);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 105);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 106);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 107);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 108);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 109);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 110);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 111);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 112);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 113);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 114);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 115);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 116);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 117);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 500);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 501);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1000);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1001);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1002);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1003);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1004);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1005);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1006);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1007);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1008);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1009);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1010);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1011);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1012);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1013);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1014);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1015);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1016);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1017);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1018);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1019);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1020);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1021);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1022);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1023);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1024);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1025);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1026);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1027);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1028);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1029);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1030);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1031);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1032);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1033);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1034);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1035);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1036);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1037);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1038);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1039);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1040);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1041);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1042);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1043);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1044);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1045);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1046);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1047);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1048);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1049);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1050);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1051);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1052);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1053);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1054);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1055);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1056);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1057);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1058);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1059);
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES (2, 1060);
COMMIT;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) COLLATE utf8mb4_general_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号码',
  `sex` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密码',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息表';

-- ----------------------------
-- Records of sys_user
-- ----------------------------
BEGIN;
INSERT INTO `sys_user` (`user_id`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `pwd_update_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-08-23 17:02:34', '2025-08-18 14:18:06', 'admin', '2025-08-18 14:18:06', '', '2025-08-23 17:02:34', '管理员');
INSERT INTO `sys_user` (`user_id`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `pwd_update_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-08-18 14:18:06', '2025-08-18 14:18:06', 'admin', '2025-08-18 14:18:06', '', NULL, '测试员');
COMMIT;

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户与岗位关联表';

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
BEGIN;
INSERT INTO `sys_user_post` (`user_id`, `post_id`) VALUES (1, 1);
INSERT INTO `sys_user_post` (`user_id`, `post_id`) VALUES (2, 2);
COMMIT;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户和角色关联表';

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES (1, 1);
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES (2, 2);
COMMIT;

-- ----------------------------
-- Table structure for task_evaluation
-- ----------------------------
DROP TABLE IF EXISTS `task_evaluation`;
CREATE TABLE `task_evaluation` (
  `evaluation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `task_title` varchar(200) NOT NULL COMMENT '任务标题',
  `publisher_id` bigint NOT NULL COMMENT '发单人ID（评价人）',
  `publisher_name` varchar(50) NOT NULL COMMENT '发单人昵称',
  `receiver_id` bigint NOT NULL COMMENT '接单人ID（被评价人）',
  `receiver_name` varchar(50) NOT NULL COMMENT '接单人昵称',
  `rating` tinyint NOT NULL COMMENT '评分（1-5分）',
  `evaluation_content` text COMMENT '评价内容',
  `evaluation_tags` varchar(500) DEFAULT NULL COMMENT '评价标签（JSON格式存储）',
  `is_anonymous` char(1) DEFAULT '0' COMMENT '是否匿名评价（0否 1是）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1隐藏）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`evaluation_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_publisher_id` (`publisher_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务评价表';

-- ----------------------------
-- Table structure for task_payment
-- ----------------------------
DROP TABLE IF EXISTS `task_payment`;
CREATE TABLE `task_payment` (
  `payment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `task_title` varchar(200) NOT NULL COMMENT '任务标题',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `order_no` varchar(64) NOT NULL COMMENT '支付订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) NOT NULL DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务支付记录表';

-- ----------------------------
-- Records of task_payment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_balance
-- ----------------------------
DROP TABLE IF EXISTS `user_balance`;
CREATE TABLE `user_balance` (
  `balance_id` bigint NOT NULL AUTO_INCREMENT COMMENT '余额记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `total_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总余额',
  `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`balance_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户余额表';

-- ----------------------------
-- Records of user_balance
-- ----------------------------
BEGIN;
INSERT INTO `user_balance` (`balance_id`, `user_id`, `user_name`, `total_balance`, `available_balance`, `frozen_balance`, `total_income`, `total_withdraw`, `create_time`, `update_time`) VALUES (1, 1000, '测试用户1', 0.00, 0.00, 0.00, 0.00, 0.00, '2025-08-23 17:01:17', '2025-08-23 17:01:17');
INSERT INTO `user_balance` (`balance_id`, `user_id`, `user_name`, `total_balance`, `available_balance`, `frozen_balance`, `total_income`, `total_withdraw`, `create_time`, `update_time`) VALUES (2, 1001, '测试用户2', 0.00, 0.00, 0.00, 0.00, 0.00, '2025-08-23 17:01:17', '2025-08-23 17:01:17');
INSERT INTO `user_balance` (`balance_id`, `user_id`, `user_name`, `total_balance`, `available_balance`, `frozen_balance`, `total_income`, `total_withdraw`, `create_time`, `update_time`) VALUES (3, 1002, '测试用户', 0.00, 0.00, 0.00, 0.00, 0.00, '2025-08-23 17:01:17', '2025-08-23 17:01:17');
COMMIT;

-- ----------------------------
-- Table structure for withdraw_record
-- ----------------------------
DROP TABLE IF EXISTS `withdraw_record`;
CREATE TABLE `withdraw_record` (
  `withdraw_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `withdraw_amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `withdraw_fee` decimal(10,2) DEFAULT '0.00' COMMENT '提现手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `withdraw_type` char(1) NOT NULL DEFAULT '1' COMMENT '提现方式（1支付宝 2微信 3银行卡）',
  `withdraw_status` char(1) NOT NULL DEFAULT '0' COMMENT '提现状态（0申请中 1处理中 2提现成功 3提现失败）',
  `payee_account` varchar(100) NOT NULL COMMENT '收款账户',
  `payee_name` varchar(50) NOT NULL COMMENT '收款人姓名',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `fail_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`withdraw_id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_withdraw_status` (`withdraw_status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提现记录表';

-- ----------------------------
-- Records of withdraw_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- 任务管理菜单权限配置
-- ----------------------------

-- 任务管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2080, '任务管理', 2000, 7, 'task', 'fuguang/task/index', '', 1, 0, 'C', '0', '0', 'fuguang:task:list', 'list', 'admin', sysdate(), '', null, '任务管理菜单');

-- 任务管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2081, '任务查询', 2080, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:query', '#', 'admin', sysdate(), '', null, ''),
(2082, '任务新增', 2080, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:add', '#', 'admin', sysdate(), '', null, ''),
(2083, '任务修改', 2080, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:edit', '#', 'admin', sysdate(), '', null, ''),
(2084, '任务删除', 2080, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:remove', '#', 'admin', sysdate(), '', null, ''),
(2085, '任务导出', 2080, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:export', '#', 'admin', sysdate(), '', null, ''),
(2086, '任务审核', 2080, 6, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:audit', '#', 'admin', sysdate(), '', null, ''),
(2087, '强制完成', 2080, 7, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:complete', '#', 'admin', sysdate(), '', null, ''),
(2088, '强制取消', 2080, 8, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:task:cancel', '#', 'admin', sysdate(), '', null, '');

-- 任务类型管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2090, '任务类型管理', 2000, 8, 'taskType', 'fuguang/taskType/index', '', 1, 0, 'C', '0', '0', 'fuguang:taskType:list', 'tree', 'admin', sysdate(), '', null, '任务类型管理菜单');

-- 任务类型管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2091, '任务类型查询', 2090, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskType:query', '#', 'admin', sysdate(), '', null, ''),
(2092, '任务类型新增', 2090, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskType:add', '#', 'admin', sysdate(), '', null, ''),
(2093, '任务类型修改', 2090, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskType:edit', '#', 'admin', sysdate(), '', null, ''),
(2094, '任务类型删除', 2090, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskType:remove', '#', 'admin', sysdate(), '', null, ''),
(2095, '任务类型导出', 2090, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskType:export', '#', 'admin', sysdate(), '', null, '');

-- 任务评价管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2120, '任务评价管理', 2000, 9, 'evaluation', 'fuguang/evaluation/index', '', 1, 0, 'C', '0', '0', 'fuguang:evaluation:list', 'star', 'admin', sysdate(), '', null, '任务评价管理菜单');

-- 任务评价管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2121, '任务评价查询', 2120, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:evaluation:query', '#', 'admin', sysdate(), '', null, ''),
(2122, '任务评价新增', 2120, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:evaluation:add', '#', 'admin', sysdate(), '', null, ''),
(2123, '任务评价修改', 2120, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:evaluation:edit', '#', 'admin', sysdate(), '', null, ''),
(2124, '任务评价删除', 2120, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:evaluation:remove', '#', 'admin', sysdate(), '', null, ''),
(2125, '任务评价导出', 2120, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:evaluation:export', '#', 'admin', sysdate(), '', null, '');

-- 为管理员角色分配任务管理相关菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 2080), (1, 2081), (1, 2082), (1, 2083), (1, 2084), (1, 2085), (1, 2086), (1, 2087), (1, 2088),
(1, 2090), (1, 2091), (1, 2092), (1, 2093), (1, 2094), (1, 2095),
(1, 2120), (1, 2121), (1, 2122), (1, 2123), (1, 2124), (1, 2125);

SET FOREIGN_KEY_CHECKS = 1;
